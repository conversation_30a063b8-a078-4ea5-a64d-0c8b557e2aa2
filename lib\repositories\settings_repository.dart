import 'package:sqflite/sqflite.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/account_service.dart';

/// Repository for settings-related database operations
class SettingsRepository {
  final DatabaseService _dbService = DatabaseService.instance;

  /// Get current account ID from AccountService
  String? get _currentAccountId => AccountService.currentAccount?.id;

  /// Get a setting value by key
  Future<String?> getSetting(String key) async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'settings',
        where: 'key = ? AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [key, _currentAccountId],
        limit: 1,
      );
    } else {
      maps = await db.query(
        'settings',
        where: 'key = ? AND accountId IS NULL',
        whereArgs: [key],
        limit: 1,
      );
    }

    if (maps.isNotEmpty) {
      return maps.first['value'] as String?;
    }
    return null;
  }

  /// Set a setting value
  Future<void> setSetting(String key, String value) async {
    final db = await _dbService.database;

    // Check if setting already exists
    final existingSetting = await getSetting(key);
    
    final settingData = {
      'key': key,
      'value': value,
      'accountId': _currentAccountId,
    };

    if (existingSetting == null) {
      // Insert new setting
      await db.insert('settings', settingData);
    } else {
      // Update existing setting
      String whereClause = 'key = ?';
      List<dynamic> whereArgs = [key];
      
      if (_currentAccountId != null) {
        whereClause += ' AND (accountId = ? OR accountId IS NULL)';
        whereArgs.add(_currentAccountId);
      } else {
        whereClause += ' AND accountId IS NULL';
      }

      await db.update(
        'settings',
        settingData,
        where: whereClause,
        whereArgs: whereArgs,
      );
    }
  }

  /// Get all settings for the current account
  Future<Map<String, String>> getAllSettings() async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'settings',
        where: 'accountId = ? OR accountId IS NULL',
        whereArgs: [_currentAccountId],
      );
    } else {
      maps = await db.query(
        'settings',
        where: 'accountId IS NULL',
      );
    }

    final settings = <String, String>{};
    for (final map in maps) {
      settings[map['key'] as String] = map['value'] as String;
    }
    return settings;
  }

  /// Get multiple settings by keys
  Future<Map<String, String?>> getSettings(List<String> keys) async {
    if (keys.isEmpty) return {};

    final db = await _dbService.database;
    final placeholders = keys.map((_) => '?').join(',');
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'settings',
        where: 'key IN ($placeholders) AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [...keys, _currentAccountId],
      );
    } else {
      maps = await db.query(
        'settings',
        where: 'key IN ($placeholders) AND accountId IS NULL',
        whereArgs: keys,
      );
    }

    final settings = <String, String?>{};
    
    // Initialize all keys with null
    for (final key in keys) {
      settings[key] = null;
    }
    
    // Fill in the actual values
    for (final map in maps) {
      settings[map['key'] as String] = map['value'] as String;
    }
    
    return settings;
  }

  /// Set multiple settings at once
  Future<void> setSettings(Map<String, String> settings) async {
    final db = await _dbService.database;
    
    await db.transaction((txn) async {
      for (final entry in settings.entries) {
        final key = entry.key;
        final value = entry.value;
        
        // Check if setting already exists
        List<Map<String, dynamic>> existingMaps;
        
        if (_currentAccountId != null) {
          existingMaps = await txn.query(
            'settings',
            where: 'key = ? AND (accountId = ? OR accountId IS NULL)',
            whereArgs: [key, _currentAccountId],
            limit: 1,
          );
        } else {
          existingMaps = await txn.query(
            'settings',
            where: 'key = ? AND accountId IS NULL',
            whereArgs: [key],
            limit: 1,
          );
        }

        final settingData = {
          'key': key,
          'value': value,
          'accountId': _currentAccountId,
        };

        if (existingMaps.isEmpty) {
          // Insert new setting
          await txn.insert('settings', settingData);
        } else {
          // Update existing setting
          String whereClause = 'key = ?';
          List<dynamic> whereArgs = [key];
          
          if (_currentAccountId != null) {
            whereClause += ' AND (accountId = ? OR accountId IS NULL)';
            whereArgs.add(_currentAccountId);
          } else {
            whereClause += ' AND accountId IS NULL';
          }

          await txn.update(
            'settings',
            settingData,
            where: whereClause,
            whereArgs: whereArgs,
          );
        }
      }
    });
  }

  /// Delete a setting
  Future<void> deleteSetting(String key) async {
    final db = await _dbService.database;
    
    String whereClause = 'key = ?';
    List<dynamic> whereArgs = [key];
    
    if (_currentAccountId != null) {
      whereClause += ' AND (accountId = ? OR accountId IS NULL)';
      whereArgs.add(_currentAccountId);
    } else {
      whereClause += ' AND accountId IS NULL';
    }

    await db.delete(
      'settings',
      where: whereClause,
      whereArgs: whereArgs,
    );
  }

  /// Delete multiple settings
  Future<void> deleteSettings(List<String> keys) async {
    if (keys.isEmpty) return;

    final db = await _dbService.database;
    
    await db.transaction((txn) async {
      for (final key in keys) {
        String whereClause = 'key = ?';
        List<dynamic> whereArgs = [key];
        
        if (_currentAccountId != null) {
          whereClause += ' AND (accountId = ? OR accountId IS NULL)';
          whereArgs.add(_currentAccountId);
        } else {
          whereClause += ' AND accountId IS NULL';
        }

        await txn.delete(
          'settings',
          where: whereClause,
          whereArgs: whereArgs,
        );
      }
    });
  }

  /// Check if a setting exists
  Future<bool> hasSetting(String key) async {
    final value = await getSetting(key);
    return value != null;
  }

  /// Get setting as integer (returns null if not found or not a valid integer)
  Future<int?> getSettingAsInt(String key) async {
    final value = await getSetting(key);
    if (value == null) return null;
    return int.tryParse(value);
  }

  /// Get setting as double (returns null if not found or not a valid double)
  Future<double?> getSettingAsDouble(String key) async {
    final value = await getSetting(key);
    if (value == null) return null;
    return double.tryParse(value);
  }

  /// Get setting as boolean (returns null if not found, false for "false"/"0", true for everything else)
  Future<bool?> getSettingAsBool(String key) async {
    final value = await getSetting(key);
    if (value == null) return null;
    return value.toLowerCase() != 'false' && value != '0';
  }

  /// Set setting as integer
  Future<void> setSettingAsInt(String key, int value) async {
    await setSetting(key, value.toString());
  }

  /// Set setting as double
  Future<void> setSettingAsDouble(String key, double value) async {
    await setSetting(key, value.toString());
  }

  /// Set setting as boolean
  Future<void> setSettingAsBool(String key, bool value) async {
    await setSetting(key, value.toString());
  }

  /// Get setting with default value
  Future<String> getSettingWithDefault(String key, String defaultValue) async {
    final value = await getSetting(key);
    return value ?? defaultValue;
  }

  /// Get setting as integer with default value
  Future<int> getSettingAsIntWithDefault(String key, int defaultValue) async {
    final value = await getSettingAsInt(key);
    return value ?? defaultValue;
  }

  /// Get setting as double with default value
  Future<double> getSettingAsDoubleWithDefault(String key, double defaultValue) async {
    final value = await getSettingAsDouble(key);
    return value ?? defaultValue;
  }

  /// Get setting as boolean with default value
  Future<bool> getSettingAsBoolWithDefault(String key, bool defaultValue) async {
    final value = await getSettingAsBool(key);
    return value ?? defaultValue;
  }

  /// Clear all settings for the current account
  Future<void> clearAllSettings() async {
    final db = await _dbService.database;
    
    if (_currentAccountId != null) {
      await db.delete(
        'settings',
        where: 'accountId = ? OR accountId IS NULL',
        whereArgs: [_currentAccountId],
      );
    } else {
      await db.delete(
        'settings',
        where: 'accountId IS NULL',
      );
    }
  }

  /// Get settings count
  Future<int> getSettingsCount() async {
    final db = await _dbService.database;
    
    String query = 'SELECT COUNT(*) as count FROM settings WHERE 1=1';
    List<dynamic> args = [];

    if (_currentAccountId != null) {
      query += ' AND (accountId = ? OR accountId IS NULL)';
      args.add(_currentAccountId);
    } else {
      query += ' AND accountId IS NULL';
    }

    final result = await db.rawQuery(query, args);
    return result.first['count'] as int;
  }
}
