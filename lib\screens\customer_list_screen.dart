import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing/controllers/customer_controller.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/forms/customer_form_screen.dart';
import 'package:tubewell_water_billing/screens/customer_detail_screen.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/widgets/universal_fab.dart';
import 'package:tubewell_water_billing/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing/widgets/app_drawer.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/customer_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/universal_pdf_service.dart';

class CustomerListScreen extends StatefulWidget {
  const CustomerListScreen({super.key});

  @override
  State<CustomerListScreen> createState() => _CustomerListScreenState();
}

class _CustomerListScreenState extends State<CustomerListScreen> {
  Timer? _debounce;
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Listen to text changes to update the UI for the clear button
    _searchController.addListener(() {
      setState(() {}); // Rebuild to show/hide clear button
    });
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      // Access the controller via Provider.of without listening
      Provider.of<CustomerController>(context, listen: false).search(query);
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => CustomerController(),
      child: Consumer<CustomerController>(
        builder: (context, controller, child) {
          return Scaffold(
            appBar: TubewellAppBar(
              title: 'All Customers',
              actions: [
                IconButton(
                  icon: const Icon(Icons.picture_as_pdf),
                  onPressed: () => _generatePDF(context, controller),
                  tooltip: 'Generate PDF Report',
                ),
              ],
            ),
            drawer: const AppDrawer(currentScreen: 'customers'),
            body: Column(
              children: [
                _buildSearchAndSortBar(context, controller),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: () => controller.refresh(),
                    child: _buildContent(context, controller),
                  ),
                ),
              ],
            ),
            floatingActionButton: UniversalFab(
              type: FabType.customer,
              onPressed: () => _navigateToCustomerForm(context),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchAndSortBar(BuildContext context, CustomerController controller) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              onChanged: _onSearchChanged,
              decoration: InputDecoration(
                hintText: 'Search customers...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _onSearchChanged('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 8),
          PopupMenuButton<CustomerSortOption>(
            icon: const Icon(Icons.sort),
            tooltip: 'Sort Options',
            onSelected: (value) => controller.sort(value),
            itemBuilder: (context) => CustomerSortOption.values.map((option) =>
              PopupMenuItem(
                value: option,
                child: Text(option.label),
              ),
            ).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, CustomerController controller) {
    if (controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (controller.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              controller.error!,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                controller.clearError();
                controller.refresh();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (controller.customers.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.people_outline,
        title: 'No Customers Found',
        message: controller.searchQuery.isNotEmpty
            ? 'No customers match your search criteria'
            : 'Start by adding your first customer',
        buttonText: 'Add Customer',
        onButtonPressed: () => _navigateToCustomerForm(context),
      );
    }

    return Column(
      children: [
        _buildSummaryCard(context, controller),
        Expanded(
          child: NotificationListener<ScrollNotification>(
            onNotification: (scrollInfo) {
              if (scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent - 500) {
                controller.fetchMoreCustomers();
              }
              return false;
            },
            child: ListView.builder(
              padding: const EdgeInsets.all(8),
              itemCount: controller.customers.length + (controller.isLoadingMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index >= controller.customers.length) {
                  return const Padding(
                    padding: EdgeInsets.all(16),
                    child: Center(child: CircularProgressIndicator()),
                  );
                }
                
                final customerVM = controller.customers[index];
                return _CustomerListItem(
                  customerViewModel: customerVM,
                  onTap: () => _navigateToCustomerDetail(context, customerVM.customer),
                  onEdit: () => _navigateToCustomerForm(context, customerVM.customer),
                  onDelete: () => _deleteCustomer(context, customerVM.customer),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(BuildContext context, CustomerController controller) {
    final summary = controller.summary;
    if (summary.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryItem(
              'Total',
              '${summary['totalCount'] ?? 0}',
              Icons.people,
              Colors.blue,
            ),
          ),
          Expanded(
            child: _buildSummaryItem(
              'Active',
              '${summary['activeCount'] ?? 0}',
              Icons.person,
              Colors.green,
            ),
          ),
          Expanded(
            child: _buildSummaryItem(
              'Credit',
              CurrencyService.formatAmount(summary['totalCredit'] ?? 0.0),
              Icons.account_balance_wallet,
              Colors.green,
            ),
          ),
          Expanded(
            child: _buildSummaryItem(
              'Due',
              CurrencyService.formatAmount(summary['totalDue'] ?? 0.0),
              Icons.payment,
              Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  // Navigation and action methods
  void _navigateToCustomerForm(BuildContext context, [Customer? customer]) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerFormScreen(customer: customer),
      ),
    );
  }

  void _navigateToCustomerDetail(BuildContext context, Customer customer) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDetailScreen(customer: customer),
      ),
    );
  }

  Future<void> _deleteCustomer(BuildContext context, Customer customer) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Customer'),
        content: Text('Are you sure you want to delete ${customer.name}? This will also delete all associated bills and payments.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await CustomerService.deleteCustomerWithAllData(customer.id);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${customer.name} deleted successfully')),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to delete customer: $e')),
          );
        }
      }
    }
  }

  Future<void> _generatePDF(BuildContext context, CustomerController controller) async {
    try {
      // Get PDF data from controller (maintains separation of concerns)
      final pdfData = await controller.generateCustomerReportData();

      if (context.mounted) {
        await UniversalPdfService.handlePdf(context, pdfData);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to generate PDF: $e')),
        );
      }
    }
  }
}

/// Simplified CustomerListItem that uses pre-calculated balance data
class _CustomerListItem extends StatelessWidget {
  final CustomerViewModel customerViewModel;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const _CustomerListItem({
    required this.customerViewModel,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final customer = customerViewModel.customer;
    final balance = customerViewModel.balance;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getBalanceColor(balance).withValues(alpha: 0.1),
          child: Icon(
            Icons.person,
            color: _getBalanceColor(balance),
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                customer.name,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
            _buildBalanceChip(balance),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (customer.contactNumber != null)
              Text(
                customer.contactNumber!,
                style: TextStyle(color: Colors.grey[600]),
              ),
            const SizedBox(height: 4),
            Text(
              'Created: ${_formatDate(customer.createdAt)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                onEdit();
                break;
              case 'delete':
                onDelete();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 18),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 18, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildBalanceChip(double balance) {
    final color = _getBalanceColor(balance);
    final text = balance == 0
        ? 'No Balance'
        : balance > 0
            ? 'Credit: ${CurrencyService.formatAmount(balance)}'
            : 'Due: ${CurrencyService.formatAmount(balance.abs())}';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getBalanceColor(double balance) {
    if (balance > 0) return Colors.green;
    if (balance < 0) return Colors.red;
    return Colors.grey;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
