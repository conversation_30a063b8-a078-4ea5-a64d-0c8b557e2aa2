import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/repositories/repository_service.dart';
import 'package:tubewell_water_billing/services/payment_service.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';

/// Controller for managing bill detail screen state and business logic
class BillDetailController extends ChangeNotifier {
  final RepositoryService _repos = RepositoryService.instance;
  
  // State variables
  Bill? _bill;
  Customer? _customer;
  List<Payment> _payments = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  Bill? get bill => _bill;
  Customer? get customer => _customer;
  List<Payment> get payments => _payments;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  /// Initialize the controller with bill data
  Future<void> initialize(int billId) async {
    await loadBillDetails(billId);
  }

  /// Load complete bill details using optimized repository method
  Future<void> loadBillDetails(int billId) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _repos.bill.getBillDetailsOptimized(billId);
      
      if (result != null) {
        _bill = result['bill'] as Bill?;
        _customer = result['customer'] as Customer?;
        _payments = result['payments'] as List<Payment>? ?? [];
      } else {
        _setError('Bill not found');
      }
    } catch (e) {
      _setError('Error loading bill details: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh bill data (used after payments or edits)
  Future<void> refresh() async {
    if (_bill != null) {
      await loadBillDetails(_bill!.id);
    }
  }

  /// Record a payment for this bill
  Future<Map<String, dynamic>> recordPayment({
    required double amount,
    required DateTime paymentDate,
    required String paymentMethod,
    String? remarks,
  }) async {
    if (_bill == null || _customer == null) {
      return {
        'success': false,
        'error': 'Bill or customer data not available',
      };
    }

    try {
      final finalRemarks = remarks?.isEmpty == true 
          ? 'Payment for Bill #${_bill!.id}'
          : '${remarks ?? ''} (Payment for Bill #${_bill!.id})';

      final result = await PaymentService.processPayment(
        customerId: _customer!.id,
        amount: amount,
        paymentDate: paymentDate,
        paymentMethod: paymentMethod,
        remarks: finalRemarks,
        targetBillId: _bill!.id,
      );

      if (result['success'] == true) {
        // Notify other screens about the changes
        DataChangeNotifierService().notifyDataChanged(DataChangeType.payment);
        DataChangeNotifierService().notifyDataChanged(DataChangeType.bill);
        
        // Refresh the data
        await refresh();
      }

      return result;
    } catch (e) {
      return {
        'success': false,
        'error': 'Error recording payment: ${e.toString()}',
      };
    }
  }

  /// Show payment form with pre-filled amount (for "Mark as Paid" functionality)
  Map<String, dynamic> getPrefilledPaymentData() {
    if (_bill == null) {
      return {};
    }

    return {
      'amount': _bill!.outstandingAmount,
      'paymentDate': DateTime.now(),
      'paymentMethod': 'Cash',
      'remarks': '',
    };
  }

  /// Delete a payment
  Future<Map<String, dynamic>> deletePayment(Payment payment) async {
    try {
      await PaymentService.deletePaymentAndUpdateBillStatus(payment);
      
      // Notify other screens about the changes
      DataChangeNotifierService().notifyDataChanged(DataChangeType.payment);
      DataChangeNotifierService().notifyDataChanged(DataChangeType.bill);
      
      // Refresh the data
      await refresh();
      
      return {
        'success': true,
        'message': 'Payment deleted successfully',
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Error deleting payment: ${e.toString()}',
      };
    }
  }

  /// Delete the bill
  Future<Map<String, dynamic>> deleteBill() async {
    if (_bill == null) {
      return {
        'success': false,
        'error': 'Bill data not available',
      };
    }

    try {
      // First delete all associated payments
      for (final payment in _payments) {
        await PaymentService.deletePaymentAndUpdateBillStatus(payment);
      }
      
      // Then delete the bill
      await _repos.bill.deleteBill(_bill!.id);
      
      // Notify other screens about the changes
      DataChangeNotifierService().notifyDataChanged(DataChangeType.bill);
      
      return {
        'success': true,
        'message': 'Bill deleted successfully',
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Error deleting bill: ${e.toString()}',
      };
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }


}
