# Design Document

## Overview

This design document outlines the refactoring of the customer list functionality in the Flutter Tubewell Water Billing App by implementing a controller pattern with optimized database queries. The current implementation has performance issues due to N+1 query problems and lacks proper separation of concerns between UI and business logic.

The solution introduces three key components:
1. **CustomerController** - A ChangeNotifier-based controller that manages state and business logic
2. **Enhanced CustomerRepository** - An optimized repository with single-query balance calculations
3. **Refactored CustomerListScreen** - A simplified UI component that focuses purely on presentation

This architecture eliminates performance bottlenecks, improves maintainability, and establishes a clean separation between presentation and business logic layers.

## Architecture

### Current Architecture Issues

The existing customer list implementation has several architectural problems:

- **Mixed Concerns**: CustomerListScreen handles both UI rendering and business logic
- **Performance Issues**: Balance calculations require multiple database queries per customer (N+1 problem)
- **State Management**: Complex state management within UI components
- **Testing Challenges**: Business logic tightly coupled with UI makes unit testing difficult

### Proposed Architecture

The new architecture follows the Controller pattern with clear separation of concerns:

```mermaid
graph TB
    subgraph "Presentation Layer"
        CLS[CustomerListScreen]
        CLI[CustomerListItem]
        Consumer[Consumer Widget]
    end
    
    subgraph "Controller Layer"
        CC[CustomerController]
        CVM[CustomerViewModel]
    end
    
    subgraph "Repository Layer"
        CR[CustomerRepository]
        OptQuery[Optimized Queries]
    end
    
    subgraph "Data Layer"
        DB[(SQLite Database)]
        Tables[customers, bills, payments]
    end
    
    CLS --> Consumer
    Consumer --> CC
    CC --> CVM
    CC --> CR
    CR --> OptQuery
    OptQuery --> DB
    DB --> Tables
    
    CLI --> CVM
```

### Data Flow

1. **Initialization**: CustomerController automatically fetches initial data on creation
2. **State Updates**: Controller notifies UI through ChangeNotifier when data changes
3. **User Actions**: UI delegates all business operations to the controller
4. **Database Operations**: Controller uses repository for all data access
5. **Optimization**: Repository performs balance calculations in single SQL queries

## Components and Interfaces

### 1. CustomerViewModel

A view model that combines customer data with pre-calculated balance information:

```dart
class CustomerViewModel {
  final Customer customer;
  final double balance;
  
  CustomerViewModel({
    required this.customer, 
    required this.balance
  });
  
  // Convenience getters
  bool get hasCredit => balance > 0;
  bool get hasDue => balance < 0;
  String get balanceDisplay => CurrencyService.formatCurrency(balance.abs());
}
```

### 2. CustomerController

The main controller that manages customer list state and business logic:

```dart
class CustomerController with ChangeNotifier {
  final CustomerRepository _customerRepo = CustomerRepository();
  
  // State management
  List<CustomerViewModel> _customers = [];
  Map<String, dynamic> _summary = {};
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  int _currentPage = 0;
  static const int _pageSize = 20;
  
  // Filters and sorting
  String _searchQuery = '';
  String _sortOption = 'nameAsc';
  
  // Public getters
  List<CustomerViewModel> get customers => _customers;
  Map<String, dynamic> get summary => _summary;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  
  // Core methods
  Future<void> fetchInitialCustomers();
  Future<void> fetchMoreCustomers();
  Future<void> search(String query);
  Future<void> sort(String option);
  Future<void> refresh();
}
```

### 3. Enhanced CustomerRepository

An optimized repository with advanced SQL queries for performance:

```dart
class CustomerRepository {
  final DatabaseService _dbService = DatabaseService.instance;
  
  // The key optimization method
  Future<Map<String, dynamic>> getCustomersWithDetails({
    required int page,
    required int pageSize,
    String? searchQuery,
    String? sortOption,
  }) async {
    // Single optimized query with JOINs and subqueries
    // Returns both customers with balances and summary data
  }
  
  // Additional optimized methods
  Future<List<CustomerViewModel>> getCustomersWithBalance();
  Future<Map<String, dynamic>> getCustomersSummary();
}
```

### 4. Refactored CustomerListScreen

A simplified UI component that focuses on presentation:

```dart
class CustomerListScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => CustomerController(),
      child: Consumer<CustomerController>(
        builder: (context, controller, child) {
          return Scaffold(
            // Simple UI that delegates to controller
          );
        },
      ),
    );
  }
}
```

## Data Models

### Enhanced Customer Model Integration

The existing Customer model will be enhanced to work seamlessly with the new architecture:

```dart
// Existing Customer model remains unchanged
class Customer {
  final int? id;
  final String name;
  final String? contactNumber;
  final DateTime createdAt;
  // ... existing properties
}

// New CustomerViewModel wraps Customer with calculated data
class CustomerViewModel {
  final Customer customer;
  final double balance;
  final int totalBills;
  final int unpaidBills;
  final DateTime? lastPaymentDate;
  
  // Computed properties for UI
  bool get hasOutstandingBalance => balance < 0;
  bool get hasCreditBalance => balance > 0;
  String get balanceStatus => hasOutstandingBalance ? 'Due' : 'Credit';
}
```

### Database Query Optimization

The core optimization involves a single SQL query that calculates balances using JOINs and subqueries:

```sql
SELECT 
  c.*,
  (IFNULL(payments.total_paid, 0) - IFNULL(bills.total_billed, 0)) as balance,
  IFNULL(bills.bill_count, 0) as total_bills,
  IFNULL(bills.unpaid_count, 0) as unpaid_bills
FROM customers c
LEFT JOIN (
  SELECT 
    customerId, 
    SUM(amount) as total_billed,
    COUNT(*) as bill_count,
    SUM(CASE WHEN isPaid = 0 THEN 1 ELSE 0 END) as unpaid_count
  FROM bills 
  GROUP BY customerId
) bills ON c.id = bills.customerId
LEFT JOIN (
  SELECT 
    customerId, 
    SUM(amount) as total_paid 
  FROM payments 
  GROUP BY customerId
) payments ON c.id = payments.customerId
WHERE c.accountId = ? OR c.accountId IS NULL
ORDER BY c.name ASC
LIMIT ? OFFSET ?
```

## Error Handling

### Comprehensive Error Management

The controller implements robust error handling with user-friendly messages:

```dart
class CustomerController with ChangeNotifier {
  String? _errorMessage;
  bool _hasError = false;
  
  String? get errorMessage => _errorMessage;
  bool get hasError => _hasError;
  
  Future<void> _handleError(dynamic error) async {
    _hasError = true;
    _errorMessage = _getUserFriendlyMessage(error);
    notifyListeners();
  }
  
  String _getUserFriendlyMessage(dynamic error) {
    if (error is DatabaseException) {
      return 'Unable to load customer data. Please try again.';
    } else if (error is TimeoutException) {
      return 'Loading is taking longer than expected. Please check your connection.';
    }
    return 'Something went wrong. Please try again.';
  }
  
  void clearError() {
    _hasError = false;
    _errorMessage = null;
    notifyListeners();
  }
}
```

### Error Recovery Mechanisms

```dart
// Retry mechanism for failed operations
Future<void> _executeWithRetry(Future<void> Function() operation) async {
  int attempts = 0;
  const maxAttempts = 3;
  
  while (attempts < maxAttempts) {
    try {
      await operation();
      return;
    } catch (e) {
      attempts++;
      if (attempts >= maxAttempts) {
        await _handleError(e);
        return;
      }
      await Future.delayed(Duration(seconds: attempts));
    }
  }
}
```

## Testing Strategy

### Unit Testing Framework

The new architecture enables comprehensive unit testing:

```dart
// Controller testing
class CustomerControllerTest {
  late CustomerController controller;
  late MockCustomerRepository mockRepo;
  
  @setUp
  void setUp() {
    mockRepo = MockCustomerRepository();
    controller = CustomerController(repository: mockRepo);
  }
  
  @test
  void testInitialDataLoading() async {
    // Test initial data fetch
    when(mockRepo.getCustomersWithDetails(any))
        .thenAnswer((_) async => mockCustomerData);
    
    await controller.fetchInitialCustomers();
    
    expect(controller.customers.length, equals(20));
    expect(controller.isLoading, isFalse);
  }
  
  @test
  void testSearchFunctionality() async {
    // Test search operations
    await controller.search('John');
    
    verify(mockRepo.getCustomersWithDetails(
      page: 0,
      pageSize: 20,
      searchQuery: 'John',
      sortOption: 'nameAsc',
    )).called(1);
  }
}
```

### Integration Testing

```dart
class CustomerIntegrationTest {
  @test
  void testEndToEndCustomerFlow() async {
    // Test complete customer list workflow
    final controller = CustomerController();
    
    // Test initial load
    await controller.fetchInitialCustomers();
    expect(controller.customers.isNotEmpty, isTrue);
    
    // Test search
    await controller.search('test');
    
    // Test sorting
    await controller.sort('creditDesc');
    
    // Verify performance
    final stopwatch = Stopwatch()..start();
    await controller.refresh();
    stopwatch.stop();
    
    expect(stopwatch.elapsedMilliseconds, lessThan(200));
  }
}
```

### Performance Testing

```dart
class CustomerPerformanceTest {
  @test
  void testLargeDatasetPerformance() async {
    // Create test database with 10,000 customers
    await _createLargeTestDataset();
    
    final controller = CustomerController();
    final stopwatch = Stopwatch()..start();
    
    await controller.fetchInitialCustomers();
    
    stopwatch.stop();
    
    // Verify performance targets
    expect(stopwatch.elapsedMilliseconds, lessThan(200));
    expect(controller.customers.length, equals(20)); // First page
  }
}
```

## Performance Optimization Strategy

### Database-Level Optimizations

1. **Single Query Approach**: Eliminate N+1 queries by calculating all balances in one SQL operation
2. **Efficient Indexing**: Leverage existing indexes on customerId, accountId, and name columns
3. **Selective Loading**: Load only necessary columns for list display
4. **Pagination**: Implement efficient LIMIT/OFFSET pagination

### Memory Management

```dart
class CustomerController with ChangeNotifier {
  static const int _maxCachedPages = 5;
  final Map<int, List<CustomerViewModel>> _pageCache = {};
  
  void _manageCacheSize() {
    if (_pageCache.length > _maxCachedPages) {
      final oldestKey = _pageCache.keys.first;
      _pageCache.remove(oldestKey);
    }
  }
  
  @override
  void dispose() {
    _pageCache.clear();
    super.dispose();
  }
}
```

### UI Rendering Optimizations

```dart
class CustomerListItem extends StatelessWidget {
  final CustomerViewModel customerVM;
  
  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Card(
        child: ListTile(
          title: Text(customerVM.customer.name),
          subtitle: Text(_buildBalanceText()),
          // Pre-calculated balance eliminates async operations
        ),
      ),
    );
  }
  
  String _buildBalanceText() {
    // Synchronous balance display using pre-calculated data
    if (customerVM.balance > 0) {
      return 'Credit: ${customerVM.balanceDisplay}';
    } else if (customerVM.balance < 0) {
      return 'Due: ${customerVM.balanceDisplay}';
    }
    return 'No Balance';
  }
}
```

## Implementation Phases

### Phase 1: Core Infrastructure (Week 1)

1. **Create CustomerViewModel**: Define the view model class
2. **Implement CustomerController**: Basic controller with state management
3. **Add Provider Dependency**: Update pubspec.yaml with provider package
4. **Create Base Structure**: Set up the foundation for the new architecture

### Phase 2: Repository Optimization (Week 1-2)

1. **Enhance CustomerRepository**: Add the optimized `getCustomersWithDetails` method
2. **Implement SQL Optimization**: Create the single-query balance calculation
3. **Add Pagination Support**: Implement efficient pagination in the repository
4. **Test Query Performance**: Validate query performance improvements

### Phase 3: Controller Implementation (Week 2)

1. **Complete Controller Logic**: Implement all controller methods
2. **Add Error Handling**: Implement comprehensive error management
3. **Implement Caching**: Add intelligent caching for better performance
4. **Add State Management**: Complete state management implementation

### Phase 4: UI Refactoring (Week 2-3)

1. **Refactor CustomerListScreen**: Convert to use the new controller
2. **Update List Items**: Modify list items to use pre-calculated data
3. **Implement Loading States**: Add proper loading and error states
4. **Test UI Integration**: Ensure smooth UI integration

### Phase 5: Testing and Optimization (Week 3)

1. **Unit Testing**: Implement comprehensive unit tests
2. **Integration Testing**: Add integration tests for the complete flow
3. **Performance Testing**: Validate performance improvements
4. **Documentation**: Create documentation for the new architecture

## Migration Strategy

### Backward Compatibility

The refactoring maintains backward compatibility by:

1. **Preserving Existing APIs**: The CustomerRepository interface remains compatible
2. **Gradual Migration**: Other screens can continue using the old approach during transition
3. **Data Model Consistency**: The Customer model remains unchanged

### Migration Steps

1. **Deploy New Components**: Add new controller and view model classes
2. **Update CustomerListScreen**: Replace the existing implementation
3. **Test Thoroughly**: Validate functionality and performance
4. **Monitor Performance**: Track improvements in real-world usage
5. **Document Changes**: Update development documentation

## Security Considerations

### Data Access Control

The controller maintains existing security patterns:

```dart
class CustomerController with ChangeNotifier {
  String? get _currentAccountId => AccountService.currentAccount?.id;
  
  Future<void> fetchInitialCustomers() async {
    // Ensure account-based data isolation
    final result = await _customerRepo.getCustomersWithDetails(
      accountId: _currentAccountId,
      // ... other parameters
    );
  }
}
```

### Input Validation

```dart
Future<void> search(String query) async {
  // Sanitize search input
  final sanitizedQuery = query.trim();
  if (sanitizedQuery.length > 100) {
    throw ArgumentError('Search query too long');
  }
  
  _searchQuery = sanitizedQuery;
  await fetchInitialCustomers();
}
```

## Monitoring and Maintenance

### Performance Monitoring

```dart
class CustomerController with ChangeNotifier {
  Future<void> fetchInitialCustomers() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      // Fetch data
      final result = await _customerRepo.getCustomersWithDetails(/*...*/);
      
      stopwatch.stop();
      
      // Log performance metrics
      PerformanceMonitor.recordQueryTime(
        'customer_list_load', 
        stopwatch.elapsed
      );
      
    } catch (e) {
      // Handle errors
    }
  }
}
```

### Maintenance Procedures

1. **Regular Performance Reviews**: Monitor query performance monthly
2. **Cache Optimization**: Review and optimize caching strategies
3. **Database Maintenance**: Ensure indexes remain optimal
4. **Error Monitoring**: Track and analyze error patterns

This design provides a comprehensive foundation for implementing the customer controller refactoring while maintaining system stability and achieving significant performance improvements. The architecture is scalable and can serve as a template for refactoring other list-based screens in the application.