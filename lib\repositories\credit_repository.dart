import 'package:sqflite/sqflite.dart';
import 'package:tubewell_water_billing/models/customer_credit.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/account_service.dart';

/// Repository for customer credit operations
class CreditRepository {
  final DatabaseService _dbService = DatabaseService.instance;

  /// Get current account ID from AccountService
  String? get _currentAccountId => AccountService.currentAccount?.id;

  /// Get customer credit by customer ID
  Future<CustomerCredit?> getCustomerCredit(int customerId) async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'customer_credits',
        where: 'customerId = ? AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [customerId, _currentAccountId],
      );
    } else {
      maps = await db.query(
        'customer_credits',
        where: 'customerId = ? AND accountId IS NULL',
        whereArgs: [customerId],
      );
    }

    if (maps.isNotEmpty) {
      return CustomerCredit.fromMap(maps.first);
    }
    return null;
  }

  /// Get all customer credits for the current account
  Future<List<CustomerCredit>> getAllCustomerCredits() async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'customer_credits',
        where: 'accountId = ? OR accountId IS NULL',
        whereArgs: [_currentAccountId],
        orderBy: 'lastUpdated DESC',
      );
    } else {
      maps = await db.query(
        'customer_credits',
        where: 'accountId IS NULL',
        orderBy: 'lastUpdated DESC',
      );
    }

    return maps.map((map) => CustomerCredit.fromMap(map)).toList();
  }

  /// Save customer credit (insert or update)
  Future<int> saveCustomerCredit(CustomerCredit credit) async {
    final db = await _dbService.database;

    // Add the current account ID to the credit data
    final creditData = credit.toMap();
    creditData['accountId'] = _currentAccountId;

    if (credit.id == null) {
      // Insert new credit
      return await db.insert('customer_credits', creditData);
    } else {
      // Update existing credit
      await db.update(
        'customer_credits',
        creditData,
        where: 'id = ?',
        whereArgs: [credit.id],
      );
      return credit.id!;
    }
  }

  /// Update customer credit amount
  Future<void> updateCustomerCreditAmount(int customerId, double amount, {String? notes}) async {
    final db = await _dbService.database;
    
    // Check if credit record exists
    final existingCredit = await getCustomerCredit(customerId);
    
    final creditData = {
      'customerId': customerId,
      'amount': amount,
      'lastUpdated': DateTime.now().toIso8601String(),
      'notes': notes ?? '',
      'accountId': _currentAccountId,
    };

    if (existingCredit == null) {
      // Insert new credit record
      await db.insert('customer_credits', creditData);
    } else {
      // Update existing credit record
      await db.update(
        'customer_credits',
        creditData,
        where: 'customerId = ?',
        whereArgs: [customerId],
      );
    }
  }

  /// Add to customer credit (increase credit amount)
  Future<void> addToCustomerCredit(int customerId, double amount, {String? notes}) async {
    final existingCredit = await getCustomerCredit(customerId);
    final currentAmount = existingCredit?.amount ?? 0.0;
    final newAmount = currentAmount + amount;
    
    await updateCustomerCreditAmount(customerId, newAmount, notes: notes);
  }

  /// Subtract from customer credit (decrease credit amount)
  Future<void> subtractFromCustomerCredit(int customerId, double amount, {String? notes}) async {
    final existingCredit = await getCustomerCredit(customerId);
    final currentAmount = existingCredit?.amount ?? 0.0;
    final newAmount = currentAmount - amount;
    
    await updateCustomerCreditAmount(customerId, newAmount, notes: notes);
  }

  /// Get customers with credit (non-zero credit amounts)
  Future<List<CustomerCredit>> getCustomersWithCredit() async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'customer_credits',
        where: '(accountId = ? OR accountId IS NULL) AND amount != 0',
        whereArgs: [_currentAccountId],
        orderBy: 'amount DESC',
      );
    } else {
      maps = await db.query(
        'customer_credits',
        where: 'accountId IS NULL AND amount != 0',
        orderBy: 'amount DESC',
      );
    }

    return maps.map((map) => CustomerCredit.fromMap(map)).toList();
  }

  /// Get customers with positive credit (credit balance > 0)
  Future<List<CustomerCredit>> getCustomersWithPositiveCredit() async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'customer_credits',
        where: '(accountId = ? OR accountId IS NULL) AND amount > 0',
        whereArgs: [_currentAccountId],
        orderBy: 'amount DESC',
      );
    } else {
      maps = await db.query(
        'customer_credits',
        where: 'accountId IS NULL AND amount > 0',
        orderBy: 'amount DESC',
      );
    }

    return maps.map((map) => CustomerCredit.fromMap(map)).toList();
  }

  /// Get customers with negative credit (credit balance < 0)
  Future<List<CustomerCredit>> getCustomersWithNegativeCredit() async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'customer_credits',
        where: '(accountId = ? OR accountId IS NULL) AND amount < 0',
        whereArgs: [_currentAccountId],
        orderBy: 'amount ASC',
      );
    } else {
      maps = await db.query(
        'customer_credits',
        where: 'accountId IS NULL AND amount < 0',
        orderBy: 'amount ASC',
      );
    }

    return maps.map((map) => CustomerCredit.fromMap(map)).toList();
  }

  /// Get credit summary
  Future<Map<String, num>> getCreditSummary() async {
    final db = await _dbService.database;
    
    String query = '''
      SELECT
        COUNT(*) as totalCount,
        SUM(amount) as totalAmount,
        SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as positiveAmount,
        SUM(CASE WHEN amount < 0 THEN amount ELSE 0 END) as negativeAmount,
        COUNT(CASE WHEN amount > 0 THEN 1 END) as positiveCount,
        COUNT(CASE WHEN amount < 0 THEN 1 END) as negativeCount
      FROM customer_credits
      WHERE 1=1
    ''';
    
    List<dynamic> args = [];

    // Account filtering
    if (_currentAccountId != null) {
      query += ' AND (accountId = ? OR accountId IS NULL)';
      args.add(_currentAccountId);
    } else {
      query += ' AND accountId IS NULL';
    }

    final results = await db.rawQuery(query, args);
    
    if (results.isNotEmpty) {
      final result = results.first;
      return {
        'totalCount': result['totalCount'] ?? 0,
        'totalAmount': result['totalAmount'] ?? 0.0,
        'positiveAmount': result['positiveAmount'] ?? 0.0,
        'negativeAmount': result['negativeAmount'] ?? 0.0,
        'positiveCount': result['positiveCount'] ?? 0,
        'negativeCount': result['negativeCount'] ?? 0,
      };
    }
    
    return {
      'totalCount': 0,
      'totalAmount': 0.0,
      'positiveAmount': 0.0,
      'negativeAmount': 0.0,
      'positiveCount': 0,
      'negativeCount': 0,
    };
  }

  /// Delete customer credit
  Future<void> deleteCustomerCredit(int customerId) async {
    final db = await _dbService.database;
    await db.delete(
      'customer_credits',
      where: 'customerId = ?',
      whereArgs: [customerId],
    );
  }

  /// Delete customer credit by ID
  Future<void> deleteCustomerCreditById(int id) async {
    final db = await _dbService.database;
    await db.delete(
      'customer_credits',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Check if customer has credit record
  Future<bool> hasCustomerCredit(int customerId) async {
    final credit = await getCustomerCredit(customerId);
    return credit != null;
  }

  /// Get customer credit amount (returns 0.0 if no credit record exists)
  Future<double> getCustomerCreditAmount(int customerId) async {
    final credit = await getCustomerCredit(customerId);
    return credit?.amount ?? 0.0;
  }

  /// Reset customer credit to zero
  Future<void> resetCustomerCredit(int customerId, {String? notes}) async {
    await updateCustomerCreditAmount(customerId, 0.0, notes: notes);
  }

  /// Get customers with credit by IDs (for efficient batch loading)
  Future<List<CustomerCredit>> getCustomerCreditsByIds(List<int> customerIds) async {
    if (customerIds.isEmpty) return [];
    
    final db = await _dbService.database;
    final placeholders = customerIds.map((_) => '?').join(',');
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'customer_credits',
        where: 'customerId IN ($placeholders) AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [...customerIds, _currentAccountId],
      );
    } else {
      maps = await db.query(
        'customer_credits',
        where: 'customerId IN ($placeholders) AND accountId IS NULL',
        whereArgs: customerIds,
      );
    }

    return maps.map((map) => CustomerCredit.fromMap(map)).toList();
  }
}
