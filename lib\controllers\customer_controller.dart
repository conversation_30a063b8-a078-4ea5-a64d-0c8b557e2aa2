import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/repositories/customer_repository.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';

/// Controller to manage state and business logic for the customer list.
/// Uses ChangeNotifier for reactive UI updates and handles all customer-related operations.
class CustomerController with ChangeNotifier {
  final CustomerRepository _customerRepo = CustomerRepository();
  late StreamSubscription _dataChangeSubscription;

  // --- STATE ---
  List<CustomerViewModel> _customers = [];
  Map<String, dynamic> _summary = {};
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  int _currentPage = 0;
  static const int _pageSize = 20;
  String? _error;

  // --- FILTERS & SORTING ---
  String _searchQuery = '';
  CustomerSortOption _sortOption = CustomerSortOption.nameAsc;

  // --- GETTERS for UI ---
  List<CustomerViewModel> get customers => _customers;
  Map<String, dynamic> get summary => _summary;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  bool get hasMoreData => _hasMoreData;
  String? get error => _error;
  String get searchQuery => _searchQuery;
  CustomerSortOption get sortOption => _sortOption;

  CustomerController() {
    // Listen for data changes that affect customers
    _dataChangeSubscription = DataChangeNotifierService().onDataChanged.listen((changeType) {
      if (changeType == DataChangeType.customer ||
          changeType == DataChangeType.payment ||
          changeType == DataChangeType.bill ||
          changeType == DataChangeType.all) {
        debugPrint('CustomerController: Refreshing due to ${changeType.toString()} change');
        refresh();
      }
    });

    // Initial fetch when the controller is created
    fetchInitialCustomers();
  }

  @override
  void dispose() {
    _dataChangeSubscription.cancel();
    super.dispose();
  }

  // --- CORE LOGIC ---
  Future<void> fetchInitialCustomers() async {
    _currentPage = 0;
    _hasMoreData = true;
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Fetch paginated, sorted, and filtered data in one optimized call
      final result = await _customerRepo.getCustomersWithDetails(
        page: _currentPage,
        pageSize: _pageSize,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        sortOption: _sortOption,
      );

      _customers = result['customers'] as List<CustomerViewModel>;
      _summary = result['summary'] as Map<String, dynamic>;
      _hasMoreData = _customers.length >= _pageSize;
      
      debugPrint('CustomerController: Fetched ${_customers.length} customers');
    } catch (e) {
      _error = 'Failed to load customers: ${e.toString()}';
      debugPrint('CustomerController: Error fetching initial customers: $e');
      _customers = [];
      _summary = {};
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> fetchMoreCustomers() async {
    if (_isLoadingMore || !_hasMoreData || _isLoading) return;

    _isLoadingMore = true;
    _currentPage++;
    notifyListeners();

    try {
      final result = await _customerRepo.getCustomersWithDetails(
        page: _currentPage,
        pageSize: _pageSize,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        sortOption: _sortOption,
      );

      final newCustomers = result['customers'] as List<CustomerViewModel>;
      _customers.addAll(newCustomers);
      _hasMoreData = newCustomers.length >= _pageSize;
      
      debugPrint('CustomerController: Loaded ${newCustomers.length} more customers');
    } catch (e) {
      _error = 'Failed to load more customers: ${e.toString()}';
      debugPrint('CustomerController: Error fetching more customers: $e');
      // Revert page increment on error
      _currentPage--;
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  // --- METHODS FOR UI TO CALL ---
  Future<void> search(String query) async {
    if (_searchQuery == query) return; // No change
    
    _searchQuery = query;
    await fetchInitialCustomers(); // Re-fetch with new search query
  }

  Future<void> sort(CustomerSortOption option) async {
    if (_sortOption == option) return; // No change

    _sortOption = option;
    await fetchInitialCustomers(); // Re-fetch with new sort option
  }
  
  Future<void> refresh() async {
    await fetchInitialCustomers();
  }

  /// Clear any error state
  void clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  /// Get a specific customer by ID from the current list
  CustomerViewModel? getCustomerById(int id) {
    try {
      return _customers.firstWhere((customer) => customer.customerId == id);
    } catch (e) {
      return null;
    }
  }

  /// Get current filter and sort state for external use (like PDF generation)
  Map<String, dynamic> getCurrentFilters() {
    return {
      'searchQuery': _searchQuery.isEmpty ? null : _searchQuery,
      'sortOption': _sortOption,
    };
  }

  /// Generate PDF report data for all customers
  /// This method fetches all customers (not just paginated ones) for comprehensive reporting
  Future<Map<String, dynamic>> generateCustomerReportData() async {
    try {
      // Fetch all customers for the report (bypassing pagination)
      final allCustomers = await _customerRepo.getAllCustomers();

      return {
        'customers': allCustomers,
        'summary': _summary,
        'filters': getCurrentFilters(),
      };
    } catch (e) {
      debugPrint('CustomerController: Error generating PDF data: $e');
      rethrow; // Let the UI handle the error display
    }
  }
}
