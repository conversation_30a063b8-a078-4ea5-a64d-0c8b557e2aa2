import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:logger/logger.dart';
import 'package:path/path.dart';
import 'package:synchronized/synchronized.dart';
import 'package:tubewell_water_billing/services/account_service.dart';

/// A lean service for managing SQLite database connection and schema
/// All query operations have been moved to specialized repository classes
class DatabaseService {
  static final Logger _logger = Logger();
  static final _initLock = Lock();
  static Database? _database;
  static String? _currentAccountId;

  // Private constructor for singleton
  DatabaseService._internal();
  static final DatabaseService instance = DatabaseService._internal();

  // Helper method to get current account ID from AccountService
  static String? get currentAccountId => AccountService.currentAccount?.id;

  /// Get the database instance, initializing if necessary
  Future<Database> get database async {
    return _initLock.synchronized(() async {
      // If already initialized with the same account, just return the db instance
      if (_database != null) return _database!;

      // Otherwise, initialize it
      _database = await _initDatabase();
      return _database!;
    });
  }

  /// Switch to a different account database
  /// Called by other services to set the active account, e.g., on login/logout
  Future<void> switchAccount(String? accountId) async {
    return _initLock.synchronized(() async {
      if (accountId == _currentAccountId && _database != null) {
        return; // No change needed
      }

      _logger.i('Switching database to account: ${accountId ?? 'default'}');

      if (_database?.isOpen == true) {
        await _database!.close();
      }

      _database = null;
      _currentAccountId = accountId;

      // Eagerly initialize the new database connection
      await database;
    });
  }

  /// Get the database path for backup/restore
  Future<String> getDatabasePath() async {
    final dir = await getApplicationDocumentsDirectory();
    final dbName = _currentAccountId != null
        ? 'account_$_currentAccountId.db'
        : 'default.db';
    return join(dir.path, dbName);
  }

  /// Get the database path for a specific account ID
  static Future<String> getDatabasePathForAccount(String accountId) async {
    final dir = await getApplicationDocumentsDirectory();
    return join(dir.path, 'account_$accountId.db');
  }

  /// Close the database connection
  Future<void> closeDatabase() async {
    return _initLock.synchronized(() async {
      if (_database != null) {
        try {
          _logger.i('Closing database connection');
          await _database!.close();
          _logger.i('Database connection closed successfully');
        } catch (e) {
          _logger.e('Error closing database: $e');
          // Continue with cleanup even if close fails
        } finally {
          // Always reset these values even if close fails
          _database = null;
          _currentAccountId = null;
        }
      }
    });
  }

  /// Initialize the database with proper error handling and recovery
  Future<Database> _initDatabase() async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final dbName = _currentAccountId != null ? 'account_$_currentAccountId.db' : 'default.db';
      final path = join(documentsDirectory.path, dbName);

      try {
        return await openDatabase(
          path,
          version: 4,
          onCreate: _onCreate,
          onUpgrade: _onUpgrade,
          onOpen: (db) => _logger.i('Database opened at $path'),
        );
      } catch (e) {
        _logger.e('Error opening database: $e. Attempting recovery.');
        // Simplified recovery: just delete and recreate on major errors
        if (await File(path).exists()) {
          await File(path).delete();
        }
        return await openDatabase(path, version: 4, onCreate: _onCreate);
      }
    } catch (e) {
      _logger.e('Error initializing database: $e');
      throw Exception('Failed to initialize database: $e');
    }
  }



  // Create the database tables
  static Future<void> _onCreate(Database db, int version) async {
    try {
      // Create Customer table
      await db.execute('''
        CREATE TABLE customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          contactNumber TEXT,
          createdAt TEXT NOT NULL,
          balance REAL NOT NULL DEFAULT 0.0,
          accountId TEXT
        )
      ''');

      // Create Bill table
      await db.execute('''
        CREATE TABLE bills (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customerId INTEGER NOT NULL,
          billDate TEXT NOT NULL,
          startTime TEXT NOT NULL,
          endTime TEXT NOT NULL,
          durationHours REAL NOT NULL,
          durationHoursWhole INTEGER NOT NULL,
          durationMinutes INTEGER NOT NULL,
          hourlyRate REAL NOT NULL,
          amount REAL NOT NULL,
          discountAmount REAL,
          discountTime REAL,
          remarks TEXT,
          isPaid INTEGER NOT NULL DEFAULT 0,
          isPartiallyPaid INTEGER NOT NULL DEFAULT 0,
          partialAmount REAL,
          paidDate TEXT,
          paidAmount REAL NOT NULL DEFAULT 0,
          accountId TEXT,
          FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
        )
      ''');

      // Create Payment table
      await db.execute('''
        CREATE TABLE payments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customerId INTEGER NOT NULL,
          billId INTEGER NOT NULL DEFAULT 0,
          paymentDate TEXT NOT NULL,
          amount REAL NOT NULL,
          paymentMethod TEXT,
          remarks TEXT,
          accountId TEXT,
          FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
        )
      ''');

      // Create CustomerCredit table
      await db.execute('''
        CREATE TABLE customer_credits (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customerId INTEGER NOT NULL UNIQUE,
          amount REAL NOT NULL,
          lastUpdated TEXT NOT NULL,
          notes TEXT NOT NULL DEFAULT '',
          accountId TEXT,
          FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
        )
      ''');

      // Create PaymentAllocation table
      await db.execute('''
        CREATE TABLE payment_allocations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          paymentId INTEGER NOT NULL,
          billId INTEGER NOT NULL,
          amount REAL NOT NULL,
          remarks TEXT,
          createdAt TEXT NOT NULL,
          accountId TEXT,
          FOREIGN KEY (paymentId) REFERENCES payments (id) ON DELETE CASCADE,
          FOREIGN KEY (billId) REFERENCES bills (id) ON DELETE CASCADE
        )
      ''');

      // Create Expenses table
      await db.execute('''
        CREATE TABLE expenses (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          description TEXT NOT NULL,
          amount REAL NOT NULL,
          date TEXT NOT NULL,
          category TEXT NOT NULL,
          paymentMethod TEXT,
          remarks TEXT,
          accountId TEXT
        )
      ''');

      // Create Settings table
      await db.execute('''
        CREATE TABLE settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          accountId TEXT,
          key TEXT NOT NULL,
          value TEXT NOT NULL,
          UNIQUE(key, accountId)
        )
      ''');

      // Create indexes
      await db.execute('CREATE INDEX idx_customers_name ON customers (name)');
      await db
          .execute('CREATE INDEX idx_bills_customerId ON bills (customerId)');
      await db.execute('CREATE INDEX idx_bills_billDate ON bills (billDate)');
      await db.execute('CREATE INDEX idx_bills_isPaid ON bills (isPaid)');
      await db.execute(
          'CREATE INDEX idx_bills_isPartiallyPaid ON bills (isPartiallyPaid)');
      await db.execute(
          'CREATE INDEX idx_payments_customerId ON payments (customerId)');
      await db.execute('CREATE INDEX idx_payments_billId ON payments (billId)');
      await db.execute(
          'CREATE INDEX idx_payments_paymentDate ON payments (paymentDate)');
      await db.execute(
          'CREATE INDEX idx_customer_credits_customerId ON customer_credits (customerId)');
      await db.execute(
          'CREATE INDEX idx_payment_allocations_createdAt ON payment_allocations (createdAt)');
      await db.execute(
          'CREATE INDEX idx_settings_key_accountId ON settings (key, accountId)');
      await db.execute('CREATE INDEX idx_expenses_date ON expenses (date)');
      await db
          .execute('CREATE INDEX idx_expenses_category ON expenses (category)');

      _logger.i('Database tables created successfully');
    } catch (e) {
      _logger.e('Error creating database tables: $e');
      throw Exception('Failed to create database tables: $e');
    }
  }



  // Upgrade the database
  static Future<void> _onUpgrade(
      Database db, int oldVersion, int newVersion) async {
    _logger.i('Upgrading database from version $oldVersion to $newVersion');

    try {
      // Handle database upgrades based on version
      if (oldVersion < 2) {
        _logger.i('Upgrading from version 1 to 2');
        await _addBalanceColumnIfNeeded(db);
      }

      if (oldVersion < 3) {
        _logger.i('Upgrading from version 2 to 3');
        // Version 3 upgrades would go here
        // This is a placeholder for future upgrades
      }

      if (oldVersion < 4) {
        _logger.i('Upgrading from version 3 to 4');
        // Ensure the balance column exists and is properly set up
        await _addBalanceColumnIfNeeded(db);

        // Verify all tables have the correct schema
        await _verifyDatabaseSchema(db);
      }

      _logger.i('Database upgrade completed successfully');
    } catch (e) {
      _logger.e('Error upgrading database: $e');

      // Try to recover by verifying the schema
      try {
        await _verifyDatabaseSchema(db);
      } catch (verifyError) {
        _logger.e('Error verifying schema during recovery: $verifyError');
      }

      // Rethrow the original error
      throw Exception('Failed to upgrade database: $e');
    }
  }

  // Helper method to add balance column if needed
  static Future<void> _addBalanceColumnIfNeeded(Database db) async {
    try {
      // Check if balance column exists
      final List<Map<String, dynamic>> tableInfo =
          await db.rawQuery("PRAGMA table_info(customers)");

      final bool hasBalanceColumn =
          tableInfo.any((column) => column['name'] == 'balance');

      // Add balance column to customers table if it doesn't exist
      if (!hasBalanceColumn) {
        _logger.i('Adding balance column to customers table');
        await db.execute(
            'ALTER TABLE customers ADD COLUMN balance REAL NOT NULL DEFAULT 0.0');
        _logger.i('Balance column added successfully');
      }

      // Update balance values for existing customers using optimized single query
      _logger.i('Updating balance values for existing customers');

      // Use a single query to calculate and update all customer balances
      await db.execute('''
        UPDATE customers
        SET balance = (
          COALESCE((
            SELECT amount
            FROM customer_credits
            WHERE customer_credits.customerId = customers.id
          ), 0.0) -
          COALESCE((
            SELECT SUM(amount - COALESCE(partialAmount, 0.0))
            FROM bills
            WHERE bills.customerId = customers.id AND bills.isPaid = 0
          ), 0.0)
        )
      ''');

      _logger.i('Customer balances updated successfully');
    } catch (e) {
      _logger.e('Error adding balance column: $e');
      rethrow; // Rethrow to be handled by the caller
    }
  }

  /// Get the current account ID
  String? getCurrentAccountId() {
    return _currentAccountId;
  }

  /// Verify database schema and fix if needed
  static Future<void> _verifyDatabaseSchema([Database? providedDb]) async {
    try {
      final db = providedDb ?? await DatabaseService.instance.database;

      // Check if customers table exists
      final List<Map<String, dynamic>> tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='customers'");

      if (tables.isEmpty) {
        _logger.w('Customers table does not exist, creating it');
        await db.execute('''
          CREATE TABLE customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contactNumber TEXT,
            createdAt TEXT NOT NULL,
            balance REAL NOT NULL DEFAULT 0.0,
            accountId TEXT
          )
        ''');
        await db.execute('CREATE INDEX idx_customers_name ON customers (name)');
        _logger.i('Customers table created successfully');
        return;
      }

      // Check if balance column exists in customers table
      final List<Map<String, dynamic>> tableInfo =
          await db.rawQuery("PRAGMA table_info(customers)");

      final bool hasBalanceColumn =
          tableInfo.any((column) => column['name'] == 'balance');

      if (!hasBalanceColumn) {
        _logger.w('Balance column missing in customers table, attempting to add it');
        try {
          await db.execute(
              'ALTER TABLE customers ADD COLUMN balance REAL NOT NULL DEFAULT 0.0');
          _logger.i('Balance column added successfully');

          // Update all existing customers to have a balance of 0.0
          await db.update('customers', {'balance': 0.0});
          _logger.i('Updated all existing customers with default balance');
        } catch (e) {
          _logger.e('Error adding balance column: $e');
          // If alter table fails, we need a more drastic approach - backup and recreate
          await _recreateCustomersTable(db);
        }
      }
    } catch (e) {
      _logger.e('Error verifying database schema: $e');
      // Try to recover by recreating the table if possible
      try {
        final db = providedDb ?? await DatabaseService.instance.database;
        await _recreateCustomersTable(db);
      } catch (recreateError) {
        _logger.e('Failed to recover from schema verification error: $recreateError');
      }
    }
  }

  /// Recreate customers table with proper schema
  static Future<void> _recreateCustomersTable(Database db) async {
    _logger.w('Attempting to recreate customers table with proper schema');

    try {
      // Begin transaction for safety
      await db.transaction((txn) async {
        List<Map<String, dynamic>> existingCustomers = [];

        // 1. Check if customers table exists and backup existing customers
        try {
          existingCustomers = await txn.query('customers');
          _logger.i('Successfully backed up ${existingCustomers.length} customers');
        } catch (e) {
          _logger.w('Could not query existing customers: $e');
          // Continue with empty list - we'll create a new table
        }

        // 2. Check if customers_new table already exists (from a previous failed attempt)
        try {
          await txn.execute('DROP TABLE IF EXISTS customers_new');
          _logger.i('Dropped existing customers_new table from previous attempt');
        } catch (e) {
          _logger.w('Error dropping customers_new table: $e');
          // Continue anyway
        }

        // 3. Create a temporary table with correct schema
        await txn.execute('''
          CREATE TABLE customers_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contactNumber TEXT,
            createdAt TEXT NOT NULL,
            balance REAL NOT NULL DEFAULT 0.0,
            accountId TEXT
          )
        ''');
        _logger.i('Created new customers_new table with correct schema');

        // 4. Copy data to new table if we have existing customers
        if (existingCustomers.isNotEmpty) {
          for (final customer in existingCustomers) {
            try {
              final Map<String, dynamic> newCustomer = {
                'id': customer['id'],
                'name': customer['name'] ?? 'Unknown',
                'contactNumber': customer['contactNumber'],
                'createdAt': customer['createdAt'] ?? DateTime.now().toIso8601String(),
                'balance': 0.0, // Default balance
                'accountId': customer['accountId']
              };

              await txn.insert('customers_new', newCustomer);
            } catch (e) {
              _logger.w('Error copying customer ${customer['id']}: $e');
              // Continue with next customer
            }
          }
          _logger.i('Copied customer data to new table');
        }

        // 5. Drop old table if it exists
        try {
          await txn.execute('DROP TABLE IF EXISTS customers');
          _logger.i('Dropped old customers table');
        } catch (e) {
          _logger.e('Error dropping customers table: $e');
          // This is critical, but try to continue
        }

        // 6. Rename new table to customers
        await txn.execute('ALTER TABLE customers_new RENAME TO customers');
        _logger.i('Renamed customers_new to customers');

        // 7. Recreate indexes
        await txn.execute('CREATE INDEX idx_customers_name ON customers (name)');
        _logger.i('Recreated indexes on customers table');
      });

      _logger.i('Successfully recreated customers table with balance column');
    } catch (e) {
      _logger.e('Error recreating customers table: $e');
      throw Exception('Failed to recreate customers table: $e');
    }
  }
}
