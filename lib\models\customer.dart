class Customer {
  int id;
  String name;
  String? contactNumber;
  DateTime createdAt;
  double balance;

  Customer({
    this.id = 0,
    required this.name,
    this.contactNumber,
    DateTime? createdAt,
    this.balance = 0.0,
  }) : createdAt = createdAt ?? DateTime.now();

  // Convert a Customer object to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id == 0 ? null : id, // SQLite will auto-assign if null
      'name': name,
      'contactNumber': contactNumber,
      'createdAt': createdAt.toIso8601String(),
      'balance': balance,
    };
  }

  // Create a Customer object from a Map
  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map['id'],
      name: map['name'],
      contactNumber: map['contactNumber'],
      createdAt: DateTime.parse(map['createdAt']),
      balance: map['balance'] != null ? map['balance'].toDouble() : 0.0,
    );
  }
}

/// Enum for customer sorting options with type safety
enum CustomerSortOption {
  nameAsc(value: 'nameAsc', label: 'Name (A-Z)'),
  nameDesc(value: 'nameDesc', label: 'Name (Z-A)'),
  creditDesc(value: 'creditDesc', label: 'Highest Credit First'),
  dueDesc(value: 'dueDesc', label: 'Highest Due First');

  const CustomerSortOption({required this.value, required this.label});

  final String value;
  final String label;

  /// Get enum from string value
  static CustomerSortOption fromValue(String value) {
    return CustomerSortOption.values.firstWhere(
      (option) => option.value == value,
      orElse: () => CustomerSortOption.nameAsc,
    );
  }
}

/// A view model that combines Customer data with calculated balance information
/// This eliminates the need for individual balance calculations in the UI
class CustomerViewModel {
  final Customer customer;
  final double balance;
  final double creditAmount;
  final double outstandingAmount;
  final bool hasOutstanding;
  final bool hasCredit;

  CustomerViewModel({
    required this.customer,
    required this.balance,
    this.creditAmount = 0.0,
    this.outstandingAmount = 0.0,
  }) : hasOutstanding = outstandingAmount > 0,
       hasCredit = creditAmount > 0;

  /// Convenience getters for common UI needs
  int get customerId => customer.id;
  String get customerName => customer.name;
  String? get contactNumber => customer.contactNumber;
  DateTime get createdAt => customer.createdAt;

  /// Returns true if the customer has a positive balance (credit)
  bool get hasPositiveBalance => balance > 0;

  /// Returns true if the customer has a negative balance (owes money)
  bool get hasNegativeBalance => balance < 0;

  /// Returns the absolute value of the balance for display purposes
  double get absoluteBalance => balance.abs();
}
