import 'package:tubewell_water_billing/repositories/customer_repository.dart';
import 'package:tubewell_water_billing/repositories/bill_repository.dart';
import 'package:tubewell_water_billing/repositories/payment_repository.dart';
import 'package:tubewell_water_billing/repositories/expense_repository.dart';
import 'package:tubewell_water_billing/repositories/credit_repository.dart';
import 'package:tubewell_water_billing/repositories/settings_repository.dart';

/// Service locator for all repository instances
/// Provides centralized access to all repositories with singleton pattern
class RepositoryService {
  static RepositoryService? _instance;
  
  // Repository instances
  late final CustomerRepository _customerRepository;
  late final BillRepository _billRepository;
  late final PaymentRepository _paymentRepository;
  late final ExpenseRepository _expenseRepository;
  late final CreditRepository _creditRepository;
  late final SettingsRepository _settingsRepository;

  RepositoryService._internal() {
    _customerRepository = CustomerRepository();
    _billRepository = BillRepository();
    _paymentRepository = PaymentRepository();
    _expenseRepository = ExpenseRepository();
    _creditRepository = CreditRepository();
    _settingsRepository = SettingsRepository();
  }

  /// Get the singleton instance
  static RepositoryService get instance {
    _instance ??= RepositoryService._internal();
    return _instance!;
  }

  /// Get customer repository
  CustomerRepository get customer => _customerRepository;

  /// Get bill repository
  BillRepository get bill => _billRepository;

  /// Get payment repository
  PaymentRepository get payment => _paymentRepository;

  /// Get expense repository
  ExpenseRepository get expense => _expenseRepository;

  /// Get credit repository
  CreditRepository get credit => _creditRepository;

  /// Get settings repository
  SettingsRepository get settings => _settingsRepository;

  /// Reset all repositories (useful for testing or account switching)
  static void reset() {
    _instance = null;
  }
}
