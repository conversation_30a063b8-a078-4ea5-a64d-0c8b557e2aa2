import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/models/payment_allocation.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing/repositories/repository_service.dart';

/// A service for handling payment operations with automatic allocation
class PaymentService {
  /// Process a payment and automatically allocate it to unpaid bills
  ///
  /// This method will:
  /// 1. Allocate payment to unpaid bills in chronological order (oldest first)
  /// 2. Mark bills as paid or partially paid based on payment amount
  /// 3. Store any unused amount as customer credit for future use
  /// 4. Use existing customer credit if available
  static Future<Map<String, dynamic>> processPayment({
    required int customerId,
    required double amount,
    required DateTime paymentDate,
    String? paymentMethod,
    String? remarks,
    int? targetBillId,
  }) async {
    // Debug: PAYMENT PROCESSING START
    // Debug: Customer ID: $customerId
    // Debug: Payment amount: $amount

    // Ensure amount is a valid non-null value
    if (amount <= 0) {
      // Debug: Warning: Invalid payment amount: $amount
      return {
        'success': false,
        'error': 'Payment amount must be greater than zero',
      };
    }

    // Get all unpaid bills for this customer, sorted by date (oldest first)
    final repos = RepositoryService.instance;
    final bills = await repos.bill.getBillsByCustomer(customerId);

    // If a target bill ID is provided, prioritize it
    if (targetBillId != null) {
      // Debug: Target bill ID provided: $targetBillId
    }

    // Filter unpaid bills
    List<Bill> unpaidBills = bills.where((bill) => !bill.isPaid).toList();

    // If target bill ID is provided, ensure it's in the list and prioritize it
    if (targetBillId != null) {
      // Find the target bill
      final targetBill = bills.firstWhere(
        (bill) => bill.id == targetBillId,
        orElse: () => Bill(
          id: 0,
          customerId: customerId,
          billDate: DateTime.now(),
          startTime: DateTime.now(),
          endTime: DateTime.now(),
          durationHours: 0,
          durationHoursWhole: 0,
          durationMinutes: 0,
          hourlyRate: 0,
          amount: 0,
        ),
      );

      // If target bill exists and is not paid, prioritize it
      if (targetBill.id == targetBillId && !targetBill.isPaid) {
        // Remove it from the list if it's already there
        unpaidBills.removeWhere((bill) => bill.id == targetBillId);

        // Add it to the beginning of the list
        unpaidBills.insert(0, targetBill);
        // Debug: Prioritizing target bill #$targetBillId
      } else if (targetBill.id == targetBillId && targetBill.isPaid) {
        // Debug: Target bill #$targetBillId is already paid
      } else {
        // Debug: Target bill #$targetBillId not found
      }
    } else {
      // If no target bill, sort by date (oldest first)
      unpaidBills.sort((a, b) => a.billDate.compareTo(b.billDate));
    }

    // Debug: Total bills: ${bills.length}
    // Debug: Unpaid bills: ${unpaidBills.length}

    // Get customer credit if available
    final customerCredit = await repos.credit.getCustomerCredit(customerId);
    double availableCredit = customerCredit?.amount ?? 0;

    // Debug: Existing credit: $availableCredit

    // Total amount available for allocation (payment + existing credit)
    double totalAvailable = amount + availableCredit;
    double remainingAmount = totalAvailable;

    // Debug: Total available for allocation: $totalAvailable

    // Results tracking
    List<Map<String, dynamic>> allocations = [];
    List<Bill> updatedBills = [];

    // Generate auto remarks if none provided
    String autoRemarks = remarks ?? '';

    // Track how much of the current payment amount was used (vs. existing credit)
    double currentPaymentUsed = 0;

    // Process each unpaid bill
    for (final bill in unpaidBills) {
      if (remainingAmount <= 0) break;

      // Ensure bill ID is valid
      if (bill.id <= 0) {
        // Debug: Warning: Found bill with invalid ID, skipping
        continue;
      }

      final billAmount = bill.isPartiallyPaid
          ? (bill.amount - (bill.partialAmount ?? 0))
          : bill.amount;

      // Debug: Processing bill #${bill.id}: amount = $billAmount

      if (remainingAmount >= billAmount) {
        // Full payment for this bill
        final updatedBill = bill.clone();
        updatedBill.isPaid = true;
        updatedBill.isPartiallyPaid = false;
        updatedBill.partialAmount = null;
        updatedBill.paidDate = paymentDate;

        await repos.bill.saveBill(updatedBill);

        updatedBills.add(updatedBill);

        // Track how much of current payment was used
        if (billAmount <= amount - currentPaymentUsed) {
          // Used from current payment
          currentPaymentUsed += billAmount;
        } else {
          // Part used from credit, part from current payment
          double fromPayment = amount - currentPaymentUsed;
          currentPaymentUsed += fromPayment;
        }

        remainingAmount -= billAmount;

        // Debug: Bill #${bill.id} marked as PAID. Remaining amount: $remainingAmount

        allocations.add({
          'billId': bill.id,
          'amount': billAmount,
          'status': 'PAID',
          'date': bill.billDate,
        });
      } else if (remainingAmount > 0) {
        // Partial payment for this bill
        final updatedBill = bill.clone();
        updatedBill.isPaid = false;
        updatedBill.isPartiallyPaid = true;

        // If already partially paid, add to the partial amount
        if (updatedBill.partialAmount != null) {
          updatedBill.partialAmount =
              updatedBill.partialAmount! + remainingAmount;
        } else {
          updatedBill.partialAmount = remainingAmount;
        }

        await repos.bill.saveBill(updatedBill);

        updatedBills.add(updatedBill);

        // Track how much of current payment was used
        if (remainingAmount <= amount - currentPaymentUsed) {
          // Used from current payment
          currentPaymentUsed += remainingAmount;
        } else {
          // Part used from credit, part from current payment
          double fromPayment = amount - currentPaymentUsed;
          currentPaymentUsed += fromPayment;
        }

        // Debug: Bill #${bill.id} marked as PARTIALLY PAID. Amount applied: $remainingAmount

        allocations.add({
          'billId': bill.id,
          'amount': remainingAmount,
          'status': 'PARTIAL',
          'date': bill.billDate,
        });

        remainingAmount = 0;
      }
    }

    // Calculate the new credit amount, taking into account what was actually used
    double newCreditAmount = remainingAmount;

    // Store any remaining amount as customer credit
    if (newCreditAmount > 0) {
      // Debug: Storing credit: $newCreditAmount
      await repos.credit.updateCustomerCreditAmount(
          customerId, newCreditAmount);

      // Track allocation to credit
      allocations.add({
        'billId': 0, // Use 0 instead of null for credit
        'amount': newCreditAmount,
        'status': 'CREDIT',
      });
    }

    // Create a single payment record for the total amount
    // Generate detailed allocation summary for remarks
    String allocationSummary = '';
    int? primaryBillId;

    if (allocations.isNotEmpty) {
      // Try to find a primary bill ID (first non-credit allocation)
      for (final allocation in allocations) {
        final billId = allocation['billId'] as int;
        if (billId > 0) {
          primaryBillId = billId;
          break;
        }
      }

      // Build allocation summary
      for (final allocation in allocations) {
        final status = allocation['status'] as String;
        final allocAmount = allocation['amount'] as double;
        final billId = allocation['billId'] as int;

        if (status == 'CREDIT') {
          allocationSummary +=
              '${CurrencyService.formatCurrency(allocAmount)} saved as credit. ';
        } else {
          allocationSummary +=
              '${CurrencyService.formatCurrency(allocAmount)} for bill #$billId (${status.toLowerCase()}). ';
        }
      }
    }

    // Combine user remarks with allocation summary
    String finalRemarks = autoRemarks.isNotEmpty ? '$autoRemarks. ' : '';
    finalRemarks += 'Allocation: $allocationSummary';

    // Create payment records for each allocation
    List<int> paymentIds = [];

    // If there are bill allocations (not just credit), create payment records for each
    List<Map<String, dynamic>> billAllocations =
        allocations.where((a) => a['billId'] > 0).toList();

    // Create a single payment record for all bill allocations
    if (billAllocations.isNotEmpty) {
      // Create one payment record for all bill allocations
      // If targetBillId is provided, use it as the primary billId
      final payment = Payment.create(
        customerId: customerId,
        billId: targetBillId ?? primaryBillId ?? 0, // Prioritize target bill ID
        paymentDate: paymentDate,
        amount: amount, // Total payment amount
        paymentMethod: paymentMethod,
        remarks: finalRemarks,
      );

      // Save the payment
      final paymentId = await repos.payment.savePayment(payment);
      if (paymentId > 0) {
        paymentIds.add(paymentId);
      }

      // Create allocation records for each bill
      for (final allocation in billAllocations) {
        final billId = allocation['billId'] as int? ?? 0;
        if (billId <= 0) continue; // Skip invalid bill IDs

        final allocAmount = allocation['amount'] as double? ?? 0.0;
        if (allocAmount <= 0) continue; // Skip zero allocations

        // Create and save allocation record
        final allocationRecord = PaymentAllocation(
          paymentId: payment.id,
          billId: billId,
          amount: allocAmount,
          remarks: 'Allocation for bill #$billId',
        );

        // Fetch the bill to make sure it exists
        final bill = await repos.bill.getBillById(billId);
        if (bill != null) {
          // Save the allocation
          await repos.payment.savePaymentAllocation(allocationRecord);
        }
      }
    }

    // If there's credit, add it as another allocation to the same payment if we already have one,
    // otherwise create a new payment record for it
    final creditAllocation = allocations
        .firstWhere((a) => a['status'] == 'CREDIT', orElse: () => {});
    if (creditAllocation.isNotEmpty) {
      final creditAmount = creditAllocation['amount'] as double? ?? 0.0;
      if (creditAmount > 0) {
        if (paymentIds.isEmpty) {
          // No payment record created yet, create one for the credit
          final creditPayment = Payment.create(
            customerId: customerId,
            billId: 0, // 0 for credit
            paymentDate: paymentDate,
            amount: creditAmount,
            paymentMethod: paymentMethod,
            remarks: 'Credit from payment: $finalRemarks',
          );

          // Save the credit payment
          final creditPaymentId =
              await repos.payment.savePayment(creditPayment);
          if (creditPaymentId > 0) {
            paymentIds.add(creditPaymentId);
          }
        } else {
          // We already have a payment, add an allocation for the credit
          final paymentId = paymentIds.first;
          if (paymentId > 0) {
            final payment = await repos.payment.getPaymentById(paymentId);
            if (payment != null) {
              final creditAllocationRecord = PaymentAllocation(
                paymentId: payment.id,
                billId: 0, // 0 for credit
                amount: creditAmount,
                remarks: 'Credit allocation',
              );

              // Save the allocation
              await repos.payment.savePaymentAllocation(
                  creditAllocationRecord);
            }
          }
        }
      }
    }

    // If no allocations were made (this should be rare/impossible), create a generic payment
    if (paymentIds.isEmpty) {
      final payment = Payment.create(
        customerId: customerId,
        billId: primaryBillId ?? 0,
        paymentDate: paymentDate,
        amount: amount,
        paymentMethod: paymentMethod,
        remarks: finalRemarks,
      );

      // Save the payment
      final paymentId = await repos.payment.savePayment(payment);
      paymentIds.add(paymentId);
    }

    // Debug: PAYMENT PROCESSING COMPLETE

    // Return results
    return {
      'success': true,
      'totalAmount': totalAvailable,
      'usedAmount': totalAvailable - remainingAmount,
      'remainingCredit': remainingAmount,
      'allocations': allocations,
      'updatedBills': updatedBills,
      'paymentIds': paymentIds,
    };
  }

  /// Get customer credit balance
  static Future<double> getCustomerCreditBalance(int customerId) async {
    try {
      final repos = RepositoryService.instance;
      final creditRecord = await repos.credit.getCustomerCredit(customerId);
      return creditRecord?.amount ?? 0;
    } catch (e) {
      // Debug: Error getting customer credit balance: $e
      return 0;
    }
  }

  /// Get a payment by its ID
  static Future<Payment?> getPaymentById(int paymentId) async {
    try {
      final repos = RepositoryService.instance;
      return await repos.payment.getPaymentById(paymentId);
    } catch (e) {
      // Debug: Error getting payment by ID: $e
      return null;
    }
  }

  /// Update an existing payment
  static Future<bool> updatePayment(Payment payment) async {
    try {
      // Save the updated payment
      final repos = RepositoryService.instance;
      await repos.payment.savePayment(payment);
      return true;
    } catch (e) {
      // Debug: Error updating payment: $e
      return false;
    }
  }

  /// Delete a payment and update the associated bill's status
  ///
  /// This method will:
  /// 1. Fetch the associated bill (if any)
  /// 2. Revert the bill status based on payment amount and current status
  /// 3. Delete the payment record
  static Future<void> deletePaymentAndUpdateBillStatus(Payment payment) async {
    try {
      // Debug: PAYMENT DELETION START
      // Debug: Deleting payment ID: ${payment.id}
      // Debug: Payment amount: ${payment.amount}
      // Debug: Linked to bill ID: ${payment.billId}

      // First find all allocation records for this payment
      final allocations = await getPaymentAllocations(payment.id);

      // Keep track of affected bill IDs to update their status
      final affectedBillIds = <int>[];

      // If no allocations exist but payment.billId is set, this is a legacy payment
      // Add that bill ID to the affected list
      if (payment.billId > 0) {
        // Debug: Adding directly linked bill ID: ${payment.billId}
        affectedBillIds.add(payment.billId);
      }

      // For each allocation, note the bill ID
      for (var allocation in allocations) {
        if (allocation.billId > 0) {
          affectedBillIds.add(allocation.billId);
        }
      }

      // Store the bill data before deletion for debugging
      final repos = RepositoryService.instance;
      Map<int, Bill> billsBeforeDeletion = {};
      for (var billId in affectedBillIds) {
        final bill = await repos.bill.getBillById(billId);
        if (bill != null) {
          billsBeforeDeletion[billId] = bill.clone();
          // Debug: Bill #$billId status before deletion: ${bill.isPaid ? "PAID" : (bill.isPartiallyPaid ? "PARTIALLY PAID" : "UNPAID")}
        }
      }

      // Delete the payment (this will cascade to delete the allocations in the database)
      await repos.payment.deletePayment(payment.id);
      // Debug: Payment deleted

      // Force an explicit update to all affected bills' status
      for (var billId in affectedBillIds) {
        await _forceUpdateBillStatus(billId);
      }

      // Also update any other unpaid bills if this was a credit payment
      if (payment.billId == 0) {
        // Debug: This was a credit payment - updating all unpaid bills
        await _updateUnpaidBillsAfterCreditChange(payment.customerId);
      }

      // Also, update the customer's credit balance
      await _updateCustomerCreditBalance(payment.customerId);

      // Notify other parts of the app that payment data has changed
      DataChangeNotifierService().notifyDataChanged(DataChangeType.payment);

      // If this payment was linked to bills, notify about bill changes too
      if (affectedBillIds.isNotEmpty) {
        DataChangeNotifierService().notifyDataChanged(DataChangeType.bill);
      }

      // Also notify about customer changes since balance is affected
      DataChangeNotifierService().notifyDataChanged(DataChangeType.customer);

      // Debug: PAYMENT DELETION COMPLETE
    } catch (e) {
      // Debug: Error deleting payment: $e
      // Debug: Stack trace: ${StackTrace.current}
      rethrow;
    }
  }

  /// Force update bill status after a payment is deleted
  static Future<void> _forceUpdateBillStatus(int billId) async {
    try {
      // Get the bill with updated payment status based on all linked payments
      final updatedBill =
          await repos.bill.getBillWithPaymentStatus(billId);
      if (updatedBill == null) {
        // Debug: Bill #$billId not found when updating status after payment deletion
        return;
      }

      // Save the updated bill
      await repos.bill.saveBill(updatedBill);

      // Debug: Bill #$billId status updated to: ${updatedBill.isPaid ? "PAID" : updatedBill.isPartiallyPaid ? "PARTIALLY PAID" : "UNPAID"}
    } catch (e) {
      // Debug: Error force updating bill status: $e
      // Debug: Stack trace: ${StackTrace.current}
    }
  }

  /// Update all unpaid bills after a credit change
  static Future<void> _updateUnpaidBillsAfterCreditChange(
      int customerId) async {
    try {
      // Get all unpaid bills for this customer, sorted by date (oldest first)
      final repos = RepositoryService.instance;
      final bills = await repos.bill.getBillsByCustomer(customerId);
      final unpaidBills = bills.where((bill) => !bill.isPaid).toList()
        ..sort((a, b) => a.billDate.compareTo(b.billDate));

      if (unpaidBills.isEmpty) {
        // Debug: No unpaid bills to update after credit change
        return;
      }

      // Debug: Found ${unpaidBills.length} unpaid bills to check after credit change

      // Get the current credit
      final customerCredit =
          await repos.credit.getCustomerCredit(customerId);
      final availableCredit = customerCredit?.amount ?? 0.0;

      // Debug: Available credit after payment deletion: $availableCredit

      if (availableCredit <= 0) {
        // Debug: No credit available, skipping bill updates
        return;
      }

      // Apply credit to bills in chronological order
      double remainingCredit = availableCredit;

      // Get all payments to calculate what's already been applied to bills
      // We'll use these payments in a future implementation
      await repos.payment.getPaymentsByCustomerPaginated(customerId, pageSize: 1000);

      for (final bill in unpaidBills) {
        if (remainingCredit <= 0) break;

        // Skip bills that are already fully paid
        if (bill.isPaid) continue;

        // Calculate the unpaid portion of this bill
        double unpaidAmount = bill.amount;
        if (bill.isPartiallyPaid && bill.partialAmount != null) {
          unpaidAmount -= bill.partialAmount!;
        }

        if (unpaidAmount <= 0) continue;

        // Determine how much credit to apply to this bill
        double creditToApply =
            remainingCredit >= unpaidAmount ? unpaidAmount : remainingCredit;

        if (creditToApply > 0) {
          // Update the bill with the credit applied
          final updatedBill = bill.clone();

          // If bill was already partially paid, add to partial amount
          if (updatedBill.isPartiallyPaid &&
              updatedBill.partialAmount != null) {
            updatedBill.partialAmount =
                updatedBill.partialAmount! + creditToApply;
          } else {
            updatedBill.isPartiallyPaid = true;
            updatedBill.partialAmount = creditToApply;
          }

          // If the bill is now fully paid
          if (updatedBill.partialAmount! >= updatedBill.amount) {
            updatedBill.isPaid = true;
            updatedBill.isPartiallyPaid = false;
            updatedBill.paidDate = DateTime.now();
          }

          // Save the updated bill
          await repos.bill.saveBill(updatedBill);

          // Reduce the remaining credit
          remainingCredit -= creditToApply;
        }
      }

      // Update the customer credit amount with what's left
      if (remainingCredit != availableCredit) {
        await repos.credit.updateCustomerCreditAmount(customerId,
            remainingCredit, 'Updated after applying credit to bills');
      }
    } catch (e) {
      // Debug: Error updating unpaid bills after credit change: $e
    }
  }

  /// Update customer credit balance
  static Future<void> _updateCustomerCreditBalance(int customerId) async {
    try {
      // Instead of just summing all credit payments, we need to calculate
      // the actual remaining credit by accounting for which bills have already consumed credit

      // Get all bills for this customer
      final repos = RepositoryService.instance;
      final bills = await repos.bill.getBillsByCustomer(customerId);

      // Get all payments for this customer
      final payments = await repos.payment.getPaymentsByCustomerPaginated(customerId, pageSize: 1000);

      // Calculate all customer initial credit (from overpayments)
      double initialCredit = 0;
      for (final payment in payments) {
        // Only consider payments that were recorded specifically as credit (billId = 0)
        if (payment.billId == 0) {
          initialCredit += payment.amount;
        }
      }

      // Now calculate how much credit has been consumed by bills
      double consumedCredit = 0;

      // Get unpaid bills
      final unpaidBills = bills.where((bill) => !bill.isPaid).toList();

      // Sort bills by date (oldest first) to match allocation order
      unpaidBills.sort((a, b) => a.billDate.compareTo(b.billDate));

      // Distribute credit to bills to determine how much has been consumed
      double remainingCredit = initialCredit;

      // For each bill that's partially paid, assume credit was used in that order
      for (final bill in bills) {
        if (bill.isPartiallyPaid &&
            bill.partialAmount != null &&
            bill.partialAmount! > 0) {
          // For partially paid bills, the partialAmount may include consumed credit
          // We'll estimate it based on the total credit and payment amounts

          // Get direct payments linked to this bill
          final directPayments =
              payments.where((p) => p.billId == bill.id).toList();
          double directPaymentAmount = 0;

          for (final payment in directPayments) {
            directPaymentAmount += payment.amount;
          }

          // If partialAmount is greater than direct payments, the difference might be from credit
          if (bill.partialAmount! > directPaymentAmount) {
            double potentialCreditUsed =
                bill.partialAmount! - directPaymentAmount;

            // But we can only consume what credit is available
            double actualCreditUsed = potentialCreditUsed > remainingCredit
                ? remainingCredit
                : potentialCreditUsed;

            consumedCredit += actualCreditUsed;
            remainingCredit -= actualCreditUsed;
          }
        }
      }

      // Calculate actual remaining credit
      double actualCredit = initialCredit - consumedCredit;
      if (actualCredit < 0) actualCredit = 0;

      // Update credit balance
      await repos.credit.updateCustomerCreditAmount(
          customerId, actualCredit, 'Updated after payment recalculation');
    } catch (e) {
      // Debug: Error updating customer credit balance: $e
    }
  }

  /// Create a payment allocation between a payment and bill
  static Future<void> createPaymentAllocation(
      int paymentId, int billId, double amount,
      {String? remarks}) async {
    try {
      final repos = RepositoryService.instance;
      final payment = await repos.payment.getPaymentById(paymentId);
      final bill = await repos.bill.getBillById(billId);

      if (payment == null) {
        throw Exception('Payment not found');
      }

      if (bill == null) {
        throw Exception('Bill not found');
      }

      // For now, we'll implement a simpler version that doesn't use the PaymentAllocation model
      // Since we're having issues with the Isar database access

      // Update the payment record to link to this bill if not already linked
      if (payment.billId == 0) {
        final updatedPayment = payment.clone();
        updatedPayment.billId = billId;

        // Add remarks about this allocation if not already present
        if (remarks != null) {
          updatedPayment.remarks = remarks;
        }

        // Save the updated payment
        await repos.payment.savePayment(updatedPayment);
      }

      // Update the bill status
      await _updateBillStatusByBillId(bill.id);
    } catch (e) {
      // Debug: Error creating payment allocation: $e
      rethrow;
    }
  }

  /// Update a bill's status based on its payment records
  static Future<void> _updateBillStatusByBillId(int billId) async {
    try {
      // Get the bill with updated payment status based on all linked payments
      final updatedBill =
          await repos.bill.getBillWithPaymentStatus(billId);
      if (updatedBill == null) {
        return;
      }

      // Save the updated bill
      await repos.bill.saveBill(updatedBill);
    } catch (e) {
      // Debug: Error updating bill status: $e
    }
  }

  /// Get all allocations for a payment
  static Future<List<PaymentAllocation>> getPaymentAllocations(
      int paymentId) async {
    try {
      // Use the payment repository to get payment allocations
      final repos = RepositoryService.instance;
      return await repos.payment.getPaymentAllocationsByPaymentId(paymentId);
    } catch (e) {
      // Debug: Error fetching payment allocations: $e
      return [];
    }
  }

  /// Get all allocations for a bill
  static Future<List<PaymentAllocation>> getBillAllocations(int billId) async {
    try {
      // Use the payment repository to get bill allocations
      final repos = RepositoryService.instance;
      return await repos.payment.getPaymentAllocationsByBillId(billId);
    } catch (e) {
      // Debug: Error fetching bill allocations: $e
      return [];
    }
  }

  /// Resets a customer's credit balance to zero
  static Future<bool> resetCustomerCredit(int customerId) async {
    try {
      // Debug: Resetting credit for customer $customerId
      final repos = RepositoryService.instance;
      await repos.credit.updateCustomerCreditAmount(
          customerId,
          0, // Setting to zero
          'Credit reset manually');
      // Debug: Credit reset result: $result
      return result;
    } catch (e) {
      // Debug: Error resetting customer credit: $e
      return false;
    }
  }

  /// Debug method to verify payments for a customer
  static Future<void> debugVerifyPaymentsForCustomer(int customerId) async {
    // Debug: ====== PAYMENT DEBUG FOR CUSTOMER $customerId ======
    try {
      // Try to get payments directly from repository
      final repos = RepositoryService.instance;
      final payments = await repos.payment.getPaymentsByCustomerPaginated(customerId, pageSize: 1000);
      // Debug: Found ${payments.length} payments for customer $customerId

      // Log each payment
      for (int i = 0; i < payments.length; i++) {
        // Payment details would be logged here in debug mode
        // ID, customer ID, bill ID, amount, date, method, remarks
      }

      // Try alternative approaches to get the payments
      // Debug: \nTrying alternative query approaches:
      try {
        final billZeroPayments = await repos.payment.getPaymentsByBill(0);
        billZeroPayments.where((p) => p.customerId == customerId).toList();
        // Debug: Found ${alternativeQuery.length} payments using bill=0 filter + customer filter
      } catch (e) {
        // Debug: Alternative query approach failed: $e
      }

      // Check all payments to find those matching this customer ID
      // Debug: \nVerifying by checking all payments:
      try {
        final allPayments = await repos.payment.getAllPayments();
        allPayments.where((p) => p.customerId == customerId).toList();
        // Debug: Found ${customerPayments.length} payments by filtering all payments
      } catch (e) {
        // Debug: All payments check failed: $e
      }
    } catch (e) {
      // Debug: Error in payment debug: $e
    }
    // Debug: ============================================
  }

  /// Repair payment records for a customer
  /// This attempts to find and fix issues with billId=0 for payments that should be associated with bills
  static Future<int> repairPaymentRecords(int customerId) async {
    // Debug: Starting payment record repair for customer $customerId
    int fixedCount = 0;

    try {
      // Get all payments for this customer
      final repos = RepositoryService.instance;
      final payments = await repos.payment.getPaymentsByCustomerPaginated(customerId, pageSize: 1000);
      // Debug: Found ${payments.length} payments to check

      // Get all bills for this customer
      final bills = await repos.bill.getBillsByCustomer(customerId);
      // Debug: Customer has ${bills.length} bills

      // Also get all bills to match by ID
      final allBills = await repos.bill.getAllBills();
      Map<int, Bill> billMap = {};
      for (var bill in allBills) {
        billMap[bill.id] = bill;
      }
      // Debug: Loaded ${billMap.length} total bills for reference

      // Check each payment
      for (final payment in payments) {
        bool needsUpdate = false;
        Payment updatedPayment = payment.clone();

        // 1. First check - Make sure customerId is set correctly
        if (payment.customerId != customerId) {
          // Debug: Payment ${payment.id} has incorrect customerId ${payment.customerId}, fixing to $customerId
          updatedPayment.customerId = customerId;
          needsUpdate = true;
        }

        // 2. Check if this is a bill payment but has billId = 0
        final remarks = payment.remarks?.toLowerCase() ?? '';

        // Try multiple patterns to extract bill references
        RegExp billRegex1 = RegExp(r'bill #(\d+)');
        RegExp billRegex2 = RegExp(r'for bill (\d+)');
        RegExp billRegex3 = RegExp(r'bill.*?(\d+)');

        int? extractedBillId;

        // Try to extract bill ID from remarks using different patterns
        void tryExtractBillId(RegExp regex) {
          if (extractedBillId != null) return; // Already found one

          final matches = regex.allMatches(remarks);
          if (matches.isNotEmpty) {
            try {
              final billIdStr = matches.first.group(1) ?? '0';
              final parsedId = int.tryParse(billIdStr) ?? 0;
              if (parsedId > 0) extractedBillId = parsedId;
            } catch (e) {
              // Debug: Error parsing bill ID: $e
            }
          }
        }

        // Try different regex patterns
        tryExtractBillId(billRegex1);
        tryExtractBillId(billRegex2);
        tryExtractBillId(billRegex3);

        // If payment mentions a bill but has billId = 0 and we found a valid bill ID
        if (payment.billId == 0 && extractedBillId != null) {
          // Debug: Payment ${payment.id} mentions bill #$extractedBillId but has billId=${payment.billId}

          // First check if this bill belongs to this customer
          bool billBelongsToCustomer =
              bills.any((b) => b.id == extractedBillId);

          if (billBelongsToCustomer && extractedBillId != null) {
            // Debug: Found matching bill #$extractedBillId for this customer, updating payment
            updatedPayment.billId = extractedBillId ?? 0;
            needsUpdate = true;
          } else if (extractedBillId != null &&
              billMap.containsKey(extractedBillId)) {
            // Check if the bill exists but belongs to a different customer
            final bill = billMap[extractedBillId]!;
            if (bill.customerId == customerId) {
              // Debug: Found matching bill #$extractedBillId in all bills, updating payment
              updatedPayment.billId = extractedBillId ?? 0;
              needsUpdate = true;
            } else {
              // Debug: Bill #$extractedBillId exists but belongs to customer #${bill.customerId}, not updating
            }
          }
        }

        // Save changes if needed
        if (needsUpdate) {
          try {
            await repos.payment.savePayment(updatedPayment);
            fixedCount++;
            // Debug: Updated payment ${payment.id}
          } catch (e) {
            // Debug: Error updating payment ${payment.id}: $e
          }
        }
      }

      // Debug: Payment repair complete. Fixed $fixedCount records
      return fixedCount;
    } catch (e) {
      // Debug: Error repairing payment records: $e
      return 0;
    }
  }
}
