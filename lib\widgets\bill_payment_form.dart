import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/controllers/bill_detail_controller.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';

/// A reusable payment form widget for recording bill payments
class BillPaymentForm extends StatefulWidget {
  final Bill bill;
  final BillDetailController controller;
  final double? initialAmount;
  final DateTime? initialPaymentDate;
  final String? initialPaymentMethod;
  final String? initialRemarks;

  const BillPaymentForm({
    super.key,
    required this.bill,
    required this.controller,
    this.initialAmount,
    this.initialPaymentDate,
    this.initialPaymentMethod,
    this.initialRemarks,
  });

  /// Static method to show the payment form as a modal bottom sheet
  static void show({
    required BuildContext context,
    required Bill bill,
    required BillDetailController controller,
    double? initialAmount,
    DateTime? initialPaymentDate,
    String? initialPaymentMethod,
    String? initialRemarks,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return BillPaymentForm(
          bill: bill,
          controller: controller,
          initialAmount: initialAmount,
          initialPaymentDate: initialPaymentDate,
          initialPaymentMethod: initialPaymentMethod,
          initialRemarks: initialRemarks,
        );
      },
    );
  }

  @override
  State<BillPaymentForm> createState() => _BillPaymentFormState();
}

class _BillPaymentFormState extends State<BillPaymentForm> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _remarksController = TextEditingController();
  
  late DateTime _paymentDate;
  late String _paymentMethod;
  bool _isProcessing = false;

  final List<String> _paymentMethods = [
    'Cash', 
    'Bank Transfer', 
    'Check', 
    'UPI', 
    'Other'
  ];

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    _paymentDate = widget.initialPaymentDate ?? DateTime.now();
    _paymentMethod = widget.initialPaymentMethod ?? 'Cash';
    
    // Set initial amount to the provided value or remaining amount due
    double remainingAmount = widget.initialAmount ?? widget.bill.outstandingAmount;
    _amountController.text = remainingAmount.toStringAsFixed(2);
    
    // Set initial remarks
    _remarksController.text = widget.initialRemarks ?? '';
  }

  @override
  void dispose() {
    _amountController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              const SizedBox(height: 8),
              const Divider(),
              _buildBillSummary(),
              _buildPaymentDateField(),
              _buildPaymentMethodField(),
              const SizedBox(height: 16),
              _buildAmountField(),
              const SizedBox(height: 16),
              _buildRemarksField(),
              const SizedBox(height: 24),
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Handle bar
        Center(
          child: Container(
            width: 50,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Icon(Icons.payment, color: Colors.green.shade700),
            const SizedBox(width: 8),
            Text(
              'Payment for Bill #${widget.bill.id}',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBillSummary() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, size: 16, color: Colors.blue.shade700),
              const SizedBox(width: 8),
              Text(
                'Bill Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade900,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildSummaryRow('Total Amount:', widget.bill.amount),
          if (widget.bill.isPartiallyPaid) ...[
            const SizedBox(height: 4),
            _buildSummaryRow(
              'Already Paid:', 
              widget.bill.partialAmount ?? 0,
              color: Colors.green.shade700,
            ),
            const SizedBox(height: 4),
            _buildSummaryRow(
              'Remaining:', 
              widget.bill.outstandingAmount,
              color: Colors.red.shade700,
              isBold: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, double amount, {Color? color, bool isBold = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(fontWeight: isBold ? FontWeight.bold : FontWeight.normal),
        ),
        Text(
          CurrencyService.formatCurrency(amount, decimalPlaces: 2),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentDateField() {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: const Text('Payment Date'),
      subtitle: Text(DateFormat('dd MMM yyyy').format(_paymentDate)),
      trailing: ElevatedButton.icon(
        icon: const Icon(Icons.calendar_today, size: 16),
        label: const Text('Change'),
        onPressed: _selectPaymentDate,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue.shade50,
          foregroundColor: Colors.blue.shade700,
        ),
      ),
    );
  }

  Future<void> _selectPaymentDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _paymentDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
    );
    if (picked != null && picked != _paymentDate) {
      setState(() {
        _paymentDate = picked;
      });
    }
  }

  Widget _buildPaymentMethodField() {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: 'Payment Method',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        prefixIcon: Icon(Icons.credit_card, color: Colors.blue.shade700),
      ),
      value: _paymentMethod,
      items: _paymentMethods.map((method) {
        return DropdownMenuItem<String>(
          value: method,
          child: Text(method),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _paymentMethod = value;
          });
        }
      },
    );
  }

  Widget _buildAmountField() {
    return TextFormField(
      controller: _amountController,
      decoration: InputDecoration(
        labelText: 'Amount',
        prefixText: '${CurrencyService.currentCurrency.symbol} ',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        prefixIcon: Icon(Icons.attach_money, color: Colors.green.shade700),
        helperText: 'Enter the payment amount',
      ),
      keyboardType: TextInputType.number,
      validator: _validateAmount,
    );
  }

  String? _validateAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter an amount';
    }
    final amount = double.tryParse(value);
    if (amount == null) {
      return 'Please enter a valid amount';
    }
    if (amount <= 0) {
      return 'Amount must be greater than zero';
    }
    return null;
  }

  Widget _buildRemarksField() {
    return TextFormField(
      controller: _remarksController,
      decoration: InputDecoration(
        labelText: 'Remarks (Optional)',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        prefixIcon: Icon(Icons.note, color: Colors.orange.shade700),
      ),
      maxLines: 2,
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isProcessing ? null : _submitPayment,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF2E7D32),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isProcessing
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : const Text(
                'Record Payment',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  Future<void> _submitPayment() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      // Create remarks with bill reference
      String finalRemarks = _remarksController.text.isEmpty
          ? 'Payment for Bill #${widget.bill.id}'
          : '${_remarksController.text} (Payment for Bill #${widget.bill.id})';

      // Parse payment amount
      double paymentAmount;
      try {
        paymentAmount = double.parse(_amountController.text.trim());
        if (paymentAmount <= 0) {
          throw Exception("Payment amount must be greater than zero");
        }
      } catch (e) {
        throw Exception("Invalid payment amount: ${_amountController.text}");
      }

      // Use the controller to record the payment
      final result = await widget.controller.recordPayment(
        amount: paymentAmount,
        paymentDate: _paymentDate,
        paymentMethod: _paymentMethod,
        remarks: finalRemarks,
      );

      if (result['success'] != true) {
        throw Exception(result['error'] ?? 'Payment failed');
      }

      // Success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Payment of Rs. ${paymentAmount.toStringAsFixed(2)} recorded successfully'
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Close bottom sheet
        Navigator.pop(context);
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error recording payment: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }
}
