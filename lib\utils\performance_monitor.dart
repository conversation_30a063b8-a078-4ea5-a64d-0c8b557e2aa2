import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';

/// Performance monitoring utility to track frame drops and UI blocking
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  static bool _isInitialized = false;
  static int _droppedFrameCount = 0;
  static int _totalFrameCount = 0;
  static DateTime? _lastFrameTime;
  static final List<Duration> _frameTimes = [];
  static const int _maxFrameTimeHistory = 100;

  /// Initialize performance monitoring
  static void initialize() {
    if (_isInitialized || !kDebugMode) return;
    
    _isInitialized = true;
    
    // Monitor frame rendering performance
    SchedulerBinding.instance.addPersistentFrameCallback(_onFrame);
    
    debugPrint('🔍 Performance monitoring initialized');
  }

  /// Frame callback to monitor performance
  static void _onFrame(Duration timestamp) {
    final now = DateTime.now();
    
    if (_lastFrameTime != null) {
      final frameDuration = now.difference(_lastFrameTime!);
      _frameTimes.add(frameDuration);
      
      // Keep only recent frame times
      if (_frameTimes.length > _maxFrameTimeHistory) {
        _frameTimes.removeAt(0);
      }
      
      // Check for dropped frames (>16.67ms = 60fps)
      const targetFrameTime = Duration(milliseconds: 16, microseconds: 670);
      if (frameDuration > targetFrameTime) {
        _droppedFrameCount++;
        
        // Log significant frame drops
        if (frameDuration.inMilliseconds > 50) {
          debugPrint('⚠️ Frame drop detected: ${frameDuration.inMilliseconds}ms');
        }
      }
      
      _totalFrameCount++;
      
      // Log performance summary every 1000 frames
      if (_totalFrameCount % 1000 == 0) {
        _logPerformanceSummary();
      }
    }
    
    _lastFrameTime = now;
  }

  /// Log performance summary
  static void _logPerformanceSummary() {
    if (_frameTimes.isEmpty) return;
    
    final avgFrameTime = _frameTimes.fold<int>(0, (sum, duration) => sum + duration.inMicroseconds) / _frameTimes.length;
    final dropRate = (_droppedFrameCount / _totalFrameCount * 100);
    
    debugPrint('📊 Performance Summary:');
    debugPrint('   Average frame time: ${(avgFrameTime / 1000).toStringAsFixed(2)}ms');
    debugPrint('   Frame drop rate: ${dropRate.toStringAsFixed(2)}%');
    debugPrint('   Total frames: $_totalFrameCount');
    debugPrint('   Dropped frames: $_droppedFrameCount');
  }

  /// Get current performance metrics
  static Map<String, dynamic> getMetrics() {
    if (_frameTimes.isEmpty) {
      return {
        'avgFrameTime': 0.0,
        'dropRate': 0.0,
        'totalFrames': _totalFrameCount,
        'droppedFrames': _droppedFrameCount,
      };
    }
    
    final avgFrameTime = _frameTimes.fold<int>(0, (sum, duration) => sum + duration.inMicroseconds) / _frameTimes.length;
    final dropRate = (_droppedFrameCount / _totalFrameCount * 100);
    
    return {
      'avgFrameTime': avgFrameTime / 1000, // Convert to milliseconds
      'dropRate': dropRate,
      'totalFrames': _totalFrameCount,
      'droppedFrames': _droppedFrameCount,
    };
  }

  /// Reset performance counters
  static void reset() {
    _droppedFrameCount = 0;
    _totalFrameCount = 0;
    _frameTimes.clear();
    _lastFrameTime = null;
  }

  /// Time a function execution and log if it's slow
  static Future<T> timeFunction<T>(
    String functionName,
    Future<T> Function() function, {
    Duration warningThreshold = const Duration(milliseconds: 100),
  }) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await function();
      stopwatch.stop();
      
      if (stopwatch.elapsed > warningThreshold) {
        debugPrint('⏱️ Slow function detected: $functionName took ${stopwatch.elapsedMilliseconds}ms');
      }
      
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ Function $functionName failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }

  /// Time a synchronous function execution
  static T timeFunctionSync<T>(
    String functionName,
    T Function() function, {
    Duration warningThreshold = const Duration(milliseconds: 50),
  }) {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = function();
      stopwatch.stop();
      
      if (stopwatch.elapsed > warningThreshold) {
        debugPrint('⏱️ Slow sync function detected: $functionName took ${stopwatch.elapsedMilliseconds}ms');
      }
      
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ Sync function $functionName failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }
}
