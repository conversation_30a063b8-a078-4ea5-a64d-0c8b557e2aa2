import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/controllers/bill_detail_controller.dart';

/// A dialog widget for confirming bill deletion
class DeleteBillConfirmationDialog extends StatelessWidget {
  final Bill bill;
  final Customer customer;
  final List<Payment> payments;
  final BillDetailController controller;

  const DeleteBillConfirmationDialog({
    super.key,
    required this.bill,
    required this.customer,
    required this.payments,
    required this.controller,
  });

  /// Static method to show the delete confirmation dialog
  static void show({
    required BuildContext context,
    required Bill bill,
    required Customer customer,
    required List<Payment> payments,
    required BillDetailController controller,
  }) {
    showDialog(
      context: context,
      builder: (context) => DeleteBillConfirmationDialog(
        bill: bill,
        customer: customer,
        payments: payments,
        controller: controller,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Delete Bill'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete this bill for ${customer.name}?',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('Amount: Rs. ${bill.amount.toStringAsFixed(0)}'),
            Text('Date: ${DateFormat('yyyy-MM-dd').format(bill.billDate)}'),
            if (payments.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.yellow.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.yellow.shade700),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning_amber, color: Colors.orange.shade800),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Warning: This bill has ${payments.length} payment(s)',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade800,
                            ),
                          ),
                          Text(
                            'Deleting this bill will also delete these linked payments and update the customer\'s balance accordingly. This action cannot be undone.',
                            style: TextStyle(
                              fontSize: 12, 
                              color: Colors.orange.shade900
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('CANCEL'),
        ),
        TextButton(
          onPressed: () => _handleDelete(context),
          child: const Text('DELETE', style: TextStyle(color: Colors.red)),
        ),
      ],
    );
  }

  Future<void> _handleDelete(BuildContext context) async {
    Navigator.of(context).pop(); // Close dialog first
    
    try {
      final result = await controller.deleteBill();

      if (context.mounted) {
        if (result['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'Bill deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context); // Return to previous screen
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['error'] ?? 'Error deleting bill'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting bill: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
