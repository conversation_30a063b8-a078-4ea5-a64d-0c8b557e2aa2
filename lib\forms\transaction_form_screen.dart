import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/billing_service.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/payment_service.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/widgets/message_dialog.dart';
import 'package:tubewell_water_billing/utils/navigation_helper.dart';
import 'package:tubewell_water_billing/repositories/repository_service.dart';

class TransactionFormScreen extends StatefulWidget {
  final Customer? selectedCustomer;
  final Bill? existingBill;

  const TransactionFormScreen({
    super.key,
    this.selectedCustomer,
    this.existingBill,
  });

  @override
  State<TransactionFormScreen> createState() => _TransactionFormScreenState();
}

class _TransactionFormScreenState extends State<TransactionFormScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers for form fields
  final _startTimeController = TextEditingController();
  final _endTimeController = TextEditingController();
  final _hourlyRateController = TextEditingController();
  final _remarksController = TextEditingController();
  final _partialAmountController = TextEditingController();
  final _discountAmountController = TextEditingController();
  final _discountTimeController = TextEditingController();

  // Selected values
  Customer? _selectedCustomer;
  List<Customer> _customers = [];
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _startTime = TimeOfDay.now();
  TimeOfDay _endTime = TimeOfDay(
      hour: (TimeOfDay.now().hour + 1) % 24, minute: TimeOfDay.now().minute);
  String _paymentStatus = 'unpaid'; // 'unpaid', 'partial', 'paid'

  // Calculated values
  int _durationHoursWhole = 0;
  int _durationMinutes = 0;
  double _totalAmount = 0;
  double _partialAmount = 0;
  double _discountAmount = 0;
  double _discountTimeMinutes = 0;

  // Original duration (before discount)
  int _originalDurationHoursWhole = 0;
  int _originalDurationMinutes = 0;

  // Loading state
  bool _isLoading = false;
  bool _isEditMode = false;

  // Date formatter
  final _dateFormat = DateFormat('MMM d, yyyy');

  @override
  void initState() {
    super.initState();

    // Initialize with selected customer if provided
    _selectedCustomer = widget.selectedCustomer;

    // If editing an existing bill, populate the form
    if (widget.existingBill != null) {
      _isEditMode = true;
      _populateFormWithExistingBill();
    } else {
      // Load default hourly rate
      _loadDefaultHourlyRate();
    }

    // Always load customers to enable dropdown selection
    _loadCustomers();

    // Set up listeners for time fields
    _startTimeController.addListener(_calculateDurationAndAmount);
    _endTimeController.addListener(_calculateDurationAndAmount);
    _hourlyRateController.addListener(_calculateDurationAndAmount);
  }

  Future<void> _loadDefaultHourlyRate() async {
    try {
      final defaultRate = await CurrencyService.getDefaultHourlyRate();
      if (mounted) {
        setState(() {
          _hourlyRateController.text = defaultRate.toString();
        });
      }
    } catch (e) {
      // If there's an error, use a fallback value
      if (mounted && _hourlyRateController.text.isEmpty) {
        setState(() {
          _hourlyRateController.text = '900';
        });
      }
    }
  }

  void _populateFormWithExistingBill() {
    final bill = widget.existingBill!;

    // Set the date
    _selectedDate = bill.billDate;

    // Set times
    _startTime = TimeOfDay.fromDateTime(bill.startTime);
    _endTime = TimeOfDay.fromDateTime(bill.endTime);

    // Update controllers
    _startTimeController.text = _formatTimeOfDay(_startTime);
    _endTimeController.text = _formatTimeOfDay(_endTime);
    _hourlyRateController.text = bill.hourlyRate.toString();
    _remarksController.text = bill.remarks ?? '';

    // Set discount values
    if (bill.discountAmount != null && bill.discountAmount! > 0) {
      _discountAmount = bill.discountAmount!;
      _discountAmountController.text = _discountAmount.toString();
    }

    if (bill.discountTime != null && bill.discountTime! > 0) {
      _discountTimeMinutes = bill.discountTime!;
      _discountTimeController.text = _discountTimeMinutes.toString();
    }

    // Set payment status
    if (bill.isPaid) {
      _paymentStatus = 'paid';
    } else if (bill.isPartiallyPaid) {
      _paymentStatus = 'partial';
      _partialAmountController.text = bill.partialAmount?.toString() ?? '0';
      _partialAmount = bill.partialAmount ?? 0;
    } else {
      _paymentStatus = 'unpaid';
    }

    // Calculate duration and amount
    _durationHoursWhole = bill.durationHoursWhole;
    _durationMinutes = bill.durationMinutes;
    _totalAmount = bill.amount;
  }

  Future<void> _loadCustomers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final repos = RepositoryService.instance;
      final customers = await repos.customer.getAllCustomers();

      if (customers.isEmpty) {
        if (mounted) {
          _showErrorSnackBar(
              'No customers found. Please add a customer first.');
          NavigationHelper.goBack(context);
        }
        return;
      }

      if (mounted) {
        setState(() {
          _customers = customers;

          // In edit mode, find and select the customer from the existing bill
          if (_isEditMode && widget.existingBill != null) {
            final existingCustomerId = widget.existingBill!.customerId;
            _selectedCustomer = customers.firstWhere(
              (customer) => customer.id == existingCustomerId,
              orElse: () => customers.first,
            );
          } else {
            // For new bills, use the provided customer or default to first
            _selectedCustomer = widget.selectedCustomer ?? customers.first;
          }

          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('Error loading customers: ${e.toString()}');
      }
    }
  }

  void _calculateDurationAndAmount() {
    if (_startTimeController.text.isEmpty || _endTimeController.text.isEmpty) {
      return;
    }

    final startDateTime = _combineDateAndTime(_selectedDate, _startTime);
    final endDateTime = _combineDateAndTime(_selectedDate, _endTime);

    // If end time is before start time, assume it's the next day
    final adjustedEndDateTime = endDateTime.isBefore(startDateTime)
        ? endDateTime.add(const Duration(days: 1))
        : endDateTime;

    final difference = adjustedEndDateTime.difference(startDateTime);

    // Store original duration
    _originalDurationHoursWhole = difference.inHours;
    _originalDurationMinutes = difference.inMinutes % 60;

    // Apply time discount if available
    int discountedMinutes = difference.inMinutes;
    if (_discountTimeMinutes > 0) {
      discountedMinutes = (difference.inMinutes - _discountTimeMinutes).round();
      if (discountedMinutes < 0) discountedMinutes = 0;
    }

    final durationHours = discountedMinutes / 60.0;
    final durationHoursWhole = discountedMinutes ~/ 60;
    final durationMinutes = discountedMinutes % 60;

    final hourlyRate = double.tryParse(_hourlyRateController.text) ?? 0;
    var amount = durationHours * hourlyRate;

    // Apply amount discount if available
    if (_discountAmount > 0) {
      amount = amount - _discountAmount;
      if (amount < 0) amount = 0;
    }

    setState(() {
      _durationHoursWhole = durationHoursWhole;
      _durationMinutes = durationMinutes;
      _totalAmount = amount;
    });
  }

  DateTime _combineDateAndTime(DateTime date, TimeOfDay time) {
    return DateTime(
      date.year,
      date.month,
      date.day,
      time.hour,
      time.minute,
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null && picked != _selectedDate && mounted) {
      setState(() {
        _selectedDate = picked;
      });
      _calculateDurationAndAmount();
    }
  }

  Future<void> _selectTime(BuildContext context, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime ? _startTime : _endTime,
    );

    if (picked != null && mounted) {
      setState(() {
        if (isStartTime) {
          _startTime = picked;
          _startTimeController.text = _formatTimeOfDay(picked);
        } else {
          _endTime = picked;
          _endTimeController.text = _formatTimeOfDay(picked);
        }
      });
      _calculateDurationAndAmount();
    }
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final now = DateTime.now();
    final dt = DateTime(now.year, now.month, now.day, time.hour, time.minute);
    final format = DateFormat.jm(); // 6:00 AM
    return format.format(dt);
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCustomer == null) {
      _showErrorSnackBar('Please select a customer');
      return;
    }

    // Validate partial amount if partial payment is selected
    if (_paymentStatus == 'partial') {
      final partialAmount = double.tryParse(_partialAmountController.text);
      if (partialAmount == null) {
        _showErrorSnackBar('Please enter a valid partial amount');
        return;
      }
      if (partialAmount <= 0) {
        _showErrorSnackBar('Partial amount must be greater than zero');
        return;
      }
      if (partialAmount >= _totalAmount) {
        _showErrorSnackBar('Partial amount must be less than the total amount');
        return;
      }
      _partialAmount = partialAmount;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final startDateTime = _combineDateAndTime(_selectedDate, _startTime);
      final endDateTime = _combineDateAndTime(_selectedDate, _endTime);

      // If end time is before start time, assume it's the next day
      final adjustedEndDateTime = endDateTime.isBefore(startDateTime)
          ? endDateTime.add(const Duration(days: 1))
          : endDateTime;

      final hourlyRate = double.parse(_hourlyRateController.text);

      Bill bill;

      if (_isEditMode && widget.existingBill != null) {
        // Clone the existing bill and update its properties
        bill = widget.existingBill!.clone();
        bill.customerId = _selectedCustomer!.id;
        bill.billDate = _selectedDate;
        bill.startTime = startDateTime;
        bill.endTime = adjustedEndDateTime;
        bill.hourlyRate = hourlyRate;
        bill.remarks = _remarksController.text;
        bill.isPaid = _paymentStatus == 'paid';
        bill.isPartiallyPaid = _paymentStatus == 'partial';
        if (_paymentStatus == 'partial') {
          bill.partialAmount = _partialAmount;
        } else {
          bill.partialAmount = null;
        }

        // Set discount values
        bill.discountAmount = _discountAmount > 0 ? _discountAmount : null;
        bill.discountTime =
            _discountTimeMinutes > 0 ? _discountTimeMinutes : null;

        bill.calculateBill();
      } else {
        // Create a new bill
        bill = Bill.create(
          customerId: _selectedCustomer!.id,
          startTime: startDateTime,
          endTime: adjustedEndDateTime,
          billDate: _selectedDate, // Pass the selected date
          hourlyRate: hourlyRate,
          remarks: _remarksController.text,
          discountAmount: _discountAmount > 0 ? _discountAmount : null,
          discountTime: _discountTimeMinutes > 0 ? _discountTimeMinutes : null,
        );
        bill.isPaid = _paymentStatus == 'paid';
        bill.isPartiallyPaid = _paymentStatus == 'partial';
        if (_paymentStatus == 'partial') {
          bill.partialAmount = _partialAmount;
        }
      }

      // Save to database
      int billId;
      if (_isEditMode && widget.existingBill != null) {
        // Update existing bill
        await repos.bill.saveBill(bill);
        billId = bill.id;
      } else {
        // Save new bill
        billId = await repos.bill.saveBill(bill);
        // Update the bill object with the assigned ID
        bill.id = billId;
      }

      // Handle payment transaction creation for paid bills
      if (!_isEditMode && _paymentStatus == 'paid' && billId > 0) {
        // Create a payment transaction record for bills marked as paid during creation
        final paymentAmount = bill.amount;
        final remarks = "Payment for Bill #$billId (Created as Paid)";

        // Use the payment service to create the payment record
        final result = await PaymentService.processPayment(
          customerId: _selectedCustomer!.id,
          amount: paymentAmount,
          paymentDate: _selectedDate, // Use the bill date as payment date
          paymentMethod: 'Cash', // Default payment method
          remarks: remarks,
          targetBillId: billId, // Link directly to this bill
        );

        if (result['success'] != true) {
          // If payment creation failed, show warning but don't fail the bill creation
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Bill created but payment record failed: ${result['error'] ?? 'Unknown error'}'),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      } else if (!_isEditMode && _paymentStatus == 'partial' && billId > 0 && _partialAmount > 0) {
        // Create a payment transaction record for partially paid bills
        final paymentAmount = _partialAmount;
        final remarks = "Partial payment for Bill #$billId (Created as Partially Paid)";

        // Use the payment service to create the payment record
        final result = await PaymentService.processPayment(
          customerId: _selectedCustomer!.id,
          amount: paymentAmount,
          paymentDate: _selectedDate, // Use the bill date as payment date
          paymentMethod: 'Cash', // Default payment method
          remarks: remarks,
          targetBillId: billId, // Link directly to this bill
        );

        if (result['success'] != true) {
          // If payment creation failed, show warning but don't fail the bill creation
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Bill created but payment record failed: ${result['error'] ?? 'Unknown error'}'),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      } else if (!_isEditMode && _paymentStatus != 'paid' && billId > 0) {
        // If this is a new bill and it's not already marked as paid,
        // check if there's available credit to apply
        final savedBill = await repos.bill.getBillById(billId);
        if (savedBill != null) {
          // Check and apply credit if available
          final creditApplied =
              await BillingService.checkAndApplyCreditToBill(savedBill);

          // Show a message if credit was applied
          if (creditApplied && mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'Available credit was automatically applied to this bill'),
                backgroundColor: Colors.blue,
                duration: Duration(seconds: 3),
              ),
            );
          }
        }
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Transaction saved successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Get the saved bill with its ID to show in the message dialog
        final savedBill = await repos.bill.getBillById(billId);
        if (savedBill != null && _selectedCustomer != null) {
          // Show message dialog
          if (mounted) {
            await showDialog(
              context: context,
              builder: (context) => MessageDialog(
                customer: _selectedCustomer!,
                bill: savedBill,
                title: 'Send Bill Details',
              ),
            );
          }
        }

        // Return to previous screen with smooth transition
        if (mounted) {
          NavigationHelper.goBack(context, true);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('Error saving transaction: ${e.toString()}');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void dispose() {
    _startTimeController.dispose();
    _endTimeController.dispose();
    _hourlyRateController.dispose();
    _remarksController.dispose();
    _partialAmountController.dispose();
    _discountAmountController.dispose();
    _discountTimeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'Transaction Form',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Customer selection - Always show dropdown
                    Row(
                      children: [
                        Icon(Icons.person, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        const Text(
                          'Select Customer',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<Customer>(
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.grey.shade400),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                              color: Colors.blue.shade700, width: 2),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                        hintText: 'Select a customer',
                        prefixIcon: Icon(Icons.person_outline,
                            color: Colors.blue.shade700),
                      ),
                      value: _selectedCustomer,
                      items: _customers.map((customer) {
                        return DropdownMenuItem<Customer>(
                          value: customer,
                          child: Text(customer.name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCustomer = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Please select a customer';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Date selection
                    Card(
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: [
                            Icon(Icons.calendar_today,
                                color: Colors.purple.shade700),
                            const SizedBox(width: 16),
                            const Text(
                              'Date:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 16),
                            TextButton(
                              onPressed: () => _selectDate(context),
                              style: TextButton.styleFrom(
                                backgroundColor: Colors.purple.shade50,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                              ),
                              child: Text(
                                _dateFormat.format(_selectedDate),
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.purple.shade700,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Time selection
                    Card(
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.timelapse,
                                    color: Colors.teal.shade700),
                                const SizedBox(width: 8),
                                const Text(
                                  'Time Information',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    controller: _startTimeController,
                                    readOnly: true,
                                    decoration: InputDecoration(
                                      labelText: 'Start Time',
                                      labelStyle: TextStyle(
                                          color: Colors.teal.shade700),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade400),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: BorderSide(
                                            color: Colors.teal.shade700,
                                            width: 2),
                                      ),
                                      filled: true,
                                      fillColor: Colors.teal.shade50,
                                      isCollapsed: false,
                                      isDense: true,
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              horizontal: 16, vertical: 16),
                                    ),
                                    style: const TextStyle(
                                      fontSize: 16,
                                    ),
                                    onTap: () => _selectTime(context, true),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please select start time';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TextFormField(
                                    controller: _endTimeController,
                                    readOnly: true,
                                    decoration: InputDecoration(
                                      labelText: 'End Time',
                                      labelStyle: TextStyle(
                                          color: Colors.teal.shade700),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade400),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: BorderSide(
                                            color: Colors.teal.shade700,
                                            width: 2),
                                      ),
                                      filled: true,
                                      fillColor: Colors.teal.shade50,
                                      isCollapsed: false,
                                      isDense: true,
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              horizontal: 16, vertical: 16),
                                    ),
                                    style: const TextStyle(
                                      fontSize: 16,
                                    ),
                                    onTap: () => _selectTime(context, false),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please select end time';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _discountTimeController,
                              decoration: InputDecoration(
                                labelText: 'Discount Time (minutes)',
                                labelStyle:
                                    TextStyle(color: Colors.blue.shade700),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade400),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(
                                      color: Colors.blue.shade700, width: 2),
                                ),
                                filled: true,
                                fillColor: Colors.blue.shade50,
                                prefixIcon: Icon(Icons.timer_off,
                                    color: Colors.blue.shade700),
                                helperText:
                                    'Enter minutes to discount from total time',
                              ),
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                final minutes = double.tryParse(value);
                                setState(() {
                                  _discountTimeMinutes = minutes ?? 0;
                                  _calculateDurationAndAmount();
                                });
                              },
                            ),
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.teal.shade100,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: Colors.teal.shade300),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (_discountTimeMinutes > 0) ...[
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            Icon(Icons.timer,
                                                color: Colors.blue.shade700),
                                            const SizedBox(width: 8),
                                            const Text(
                                              'Original:',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Text(
                                          '$_originalDurationHoursWhole hours $_originalDurationMinutes minutes',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.blue.shade900,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            Icon(Icons.remove_circle_outline,
                                                color: Colors.red.shade700),
                                            const SizedBox(width: 8),
                                            const Text(
                                              'Discount:',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Text(
                                          '${_discountTimeMinutes.toInt()} minutes',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.red.shade900,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    const Divider(
                                        height: 1, color: Colors.teal),
                                    const SizedBox(height: 4),
                                  ],
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(Icons.timelapse,
                                              color: Colors.teal.shade700),
                                          const SizedBox(width: 8),
                                          const Text(
                                            'Final Duration:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      Text(
                                        '$_durationHoursWhole hours $_durationMinutes minutes',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                          color: Colors.teal.shade900,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Billing information
                    Card(
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.attach_money,
                                    color: Colors.green.shade700),
                                const SizedBox(width: 8),
                                const Text(
                                  'Billing Information',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _hourlyRateController,
                              decoration: InputDecoration(
                                labelText: 'Hourly Rate (Rs.)',
                                labelStyle:
                                    TextStyle(color: Colors.green.shade700),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade400),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(
                                      color: Colors.green.shade700, width: 2),
                                ),
                                filled: true,
                                fillColor: Colors.green.shade50,
                                prefixIcon: Icon(Icons.attach_money,
                                    color: Colors.green.shade700),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter hourly rate';
                                }
                                if (double.tryParse(value) == null) {
                                  return 'Please enter a valid number';
                                }
                                return null;
                              },
                              onChanged: (value) {
                                _calculateDurationAndAmount();
                              },
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _discountAmountController,
                              decoration: InputDecoration(
                                labelText: 'Discount Amount (Rs.)',
                                labelStyle:
                                    TextStyle(color: Colors.blue.shade700),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade400),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(
                                      color: Colors.blue.shade700, width: 2),
                                ),
                                filled: true,
                                fillColor: Colors.blue.shade50,
                                prefixIcon: Icon(Icons.money_off,
                                    color: Colors.blue.shade700),
                                helperText:
                                    'Enter amount to discount from total',
                              ),
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                      decimal: true),
                              onChanged: (value) {
                                final amount = double.tryParse(value);
                                setState(() {
                                  _discountAmount = amount ?? 0;
                                  _calculateDurationAndAmount();
                                });
                              },
                            ),
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.green.shade100,
                                borderRadius: BorderRadius.circular(12),
                                border:
                                    Border.all(color: Colors.green.shade300),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (_discountAmount > 0) ...[
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            Icon(Icons.receipt_long,
                                                color: Colors.blue.shade700),
                                            const SizedBox(width: 8),
                                            const Text(
                                              'Original:',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Text(
                                          'Rs. ${(_totalAmount + _discountAmount).toStringAsFixed(2)}',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.blue.shade900,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            Icon(Icons.remove_circle_outline,
                                                color: Colors.red.shade700),
                                            const SizedBox(width: 8),
                                            const Text(
                                              'Discount:',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Text(
                                          'Rs. ${_discountAmount.toStringAsFixed(2)}',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.red.shade900,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    const Divider(
                                        height: 1, color: Colors.green),
                                    const SizedBox(height: 4),
                                  ],
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(Icons.receipt_long,
                                              color: Colors.green.shade700),
                                          const SizedBox(width: 8),
                                          const Text(
                                            'Total Amount:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      Text(
                                        'Rs. ${_totalAmount.toStringAsFixed(2)}',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18,
                                          color: Colors.green.shade900,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Payment Status
                    Card(
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.payment, color: Colors.red.shade700),
                                const SizedBox(width: 8),
                                const Text(
                                  'Payment Status',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      setState(() {
                                        _paymentStatus = 'unpaid';
                                      });
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      decoration: BoxDecoration(
                                        color: _paymentStatus == 'unpaid'
                                            ? Colors.red
                                            : Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: _paymentStatus == 'unpaid'
                                              ? Colors.red.shade700
                                              : Colors.grey.shade400,
                                          width: _paymentStatus == 'unpaid'
                                              ? 2
                                              : 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.cancel_outlined,
                                            color: _paymentStatus == 'unpaid'
                                                ? Colors.white
                                                : Colors.grey.shade700,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Unpaid',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: _paymentStatus == 'unpaid'
                                                  ? Colors.white
                                                  : Colors.grey.shade700,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      setState(() {
                                        _paymentStatus = 'partial';
                                      });
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      decoration: BoxDecoration(
                                        color: _paymentStatus == 'partial'
                                            ? Colors.purple
                                            : Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: _paymentStatus == 'partial'
                                              ? Colors.purple.shade700
                                              : Colors.grey.shade400,
                                          width: _paymentStatus == 'partial'
                                              ? 2
                                              : 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.sync_outlined,
                                            color: _paymentStatus == 'partial'
                                                ? Colors.white
                                                : Colors.grey.shade700,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Partial',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: _paymentStatus == 'partial'
                                                  ? Colors.white
                                                  : Colors.grey.shade700,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      setState(() {
                                        _paymentStatus = 'paid';
                                      });
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      decoration: BoxDecoration(
                                        color: _paymentStatus == 'paid'
                                            ? const Color(0xFF2E7D32)
                                            : Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: _paymentStatus == 'paid'
                                              ? const Color(0xFF1B5E20)
                                              : Colors.grey.shade400,
                                          width:
                                              _paymentStatus == 'paid' ? 2 : 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.check_circle_outline,
                                            color: _paymentStatus == 'paid'
                                                ? Colors.white
                                                : Colors.grey.shade700,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Paid',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: _paymentStatus == 'paid'
                                                  ? Colors.white
                                                  : Colors.grey.shade700,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            if (_paymentStatus == 'partial') ...[
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _partialAmountController,
                                decoration: InputDecoration(
                                  labelText: 'Partial Amount Paid (Rs.)',
                                  labelStyle:
                                      TextStyle(color: Colors.purple.shade700),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide:
                                        BorderSide(color: Colors.grey.shade400),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                        color: Colors.purple.shade700,
                                        width: 2),
                                  ),
                                  filled: true,
                                  fillColor: Colors.purple.shade50,
                                  prefixIcon: Icon(Icons.payments,
                                      color: Colors.purple.shade700),
                                  helperText: 'Enter amount already paid',
                                ),
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                        decimal: true),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter partial amount paid';
                                  }
                                  final amount = double.tryParse(value);
                                  if (amount == null) {
                                    return 'Please enter a valid number';
                                  }
                                  if (amount <= 0) {
                                    return 'Amount must be greater than zero';
                                  }
                                  if (amount >= _totalAmount) {
                                    return 'Partial amount must be less than total amount';
                                  }
                                  return null;
                                },
                                onChanged: (value) {
                                  final amount = double.tryParse(value);
                                  if (amount != null) {
                                    setState(() {
                                      _partialAmount = amount;
                                    });
                                  }
                                },
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.purple.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border:
                                      Border.all(color: Colors.purple.shade300),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(Icons.account_balance_wallet,
                                            color: Colors.purple.shade700),
                                        const SizedBox(width: 8),
                                        const Text(
                                          'Remaining:',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Text(
                                      'Rs. ${(_totalAmount - _partialAmount).toStringAsFixed(2)}',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                        color: Colors.purple.shade900,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Remarks
                    Card(
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.note, color: Colors.indigo.shade700),
                                const SizedBox(width: 8),
                                const Text(
                                  'Additional Information',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _remarksController,
                              decoration: InputDecoration(
                                labelText: 'Remarks (Optional)',
                                labelStyle:
                                    TextStyle(color: Colors.indigo.shade700),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade400),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(
                                      color: Colors.indigo.shade700, width: 2),
                                ),
                                filled: true,
                                fillColor: Colors.indigo.shade50,
                                prefixIcon: Icon(Icons.note,
                                    color: Colors.indigo.shade700),
                              ),
                              maxLines: 3,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Save button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveTransaction,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF2E7D32),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 4,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.save, color: Colors.white),
                            const SizedBox(width: 8),
                            Text(
                              _isEditMode
                                  ? 'Update Transaction'
                                  : 'Save Transaction',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
