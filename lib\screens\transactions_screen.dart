import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/currency.dart';
import 'package:tubewell_water_billing/repositories/repository_service.dart';
import 'package:tubewell_water_billing/services/billing_service.dart';
import 'package:tubewell_water_billing/screens/bill_details_screen.dart';
import 'package:tubewell_water_billing/screens/customer_detail_screen.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing/widgets/info_chips.dart';
import 'package:tubewell_water_billing/forms/transaction_form_screen.dart';
import 'package:tubewell_water_billing/widgets/app_drawer.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/widgets/universal_fab.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing/utils/navigation_helper.dart';

enum SortOption {
  dateNewest(label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  customerNameAZ(label: 'Customer Name (A-Z)', icon: Icons.arrow_downward),
  customerNameZA(label: 'Customer Name (Z-A)', icon: Icons.arrow_upward),
  statusUnpaidFirst(label: 'Status (Unpaid first)', icon: Icons.priority_high),
  statusPaidFirst(
      label: 'Status (Paid first)', icon: Icons.check_circle_outline);

  final String label;
  final IconData icon;

  const SortOption({
    required this.label,
    required this.icon,
  });
}

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({
    super.key,
  });

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> {
  // Pagination
  static const int pageSize = 20;
  int _currentPage = 0;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  List<Bill> _bills = [];
  List<Bill> _filteredBills = [];
  Map<int, Customer> _customersMap = {};
  bool _isLoading = true;
  bool _isRefreshing = false;

  // Group bills by date
  Map<String, List<Bill>> _groupedBills = {};
  List<String> _dateKeys = [];

  // Bills totals summary
  Map<String, num> _billsSummary = {
    'totalCount': 0,
    'totalAmount': 0,
    'paidAmount': 0,
    'unpaidAmount': 0,
    'paidCount': 0,
    'unpaidCount': 0
  };

  // For search functionality
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  // For filter functionality
  bool _isFilteringActive = false;
  DateTime? _filterStartDate;
  DateTime? _filterEndDate;
  bool? _filterPaidStatus;
  int? _filterCustomerId;

  // For sorting
  SortOption _currentSortOption = SortOption.dateNewest;

  // Scroll controller for pagination
  final ScrollController _scrollController = ScrollController();

  // Stream subscription for currency changes
  late final StreamSubscription<Currency> _currencySubscription;

  // Stream subscription for data changes
  late final StreamSubscription<DataChangeType> _dataChangeSubscription;

  @override
  void initState() {
    super.initState();
    _loadBills();
    _searchController.addListener(_onSearchChanged);
    _scrollController.addListener(_scrollListener);

    // Listen for currency changes
    _currencySubscription = CurrencyService.onCurrencyChanged.listen((_) {
      // When currency changes, refresh the UI
      if (mounted) {
        setState(() {
          // Just trigger a rebuild to update currency formatting
        });
      }
    });

    // Listen for data changes
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      // Refresh when bills or payments are changed or when a force refresh is requested
      if (changeType == DataChangeType.bill ||
          changeType == DataChangeType.payment ||
          changeType == DataChangeType.all) {
        if (mounted) {
          debugPrint(
              'TransactionsScreen: Refreshing due to ${changeType.toString()} change');
          _refreshBills();
        }
      }
    });
  }

  // We don't need to reload in didChangeDependencies since we're using DataChangeNotifierService
  // This prevents unnecessary reloads that can cause screen vibration

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _currencySubscription.cancel();
    _dataChangeSubscription.cancel();
    _debounceTimer?.cancel(); // Cancel the debounce timer
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      if (!_isLoadingMore &&
          _hasMoreData &&
          (_isSearching || _isFilteringActive)) {
        _loadMoreBills();
      }
    }
  }

  // Debounce search to reduce unnecessary rebuilds
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    _debounceTimer = Timer(_debounceDuration, () {
      if (mounted) {
        setState(() {
          _isSearching = _searchController.text.isNotEmpty;
        });
        _loadBills(resetPagination: true);
      }
    });
  }

  Future<void> _loadBills({bool resetPagination = false}) async {
    if (_isRefreshing) return;

    if (resetPagination) {
      setState(() {
        _currentPage = 0;
      });
    }

    setState(() {
      _isLoading = true;
      _isRefreshing = true;
    });

    try {
      // Load bills with filters and pagination
      final repos = RepositoryService.instance;
      final bills = await repos.bill.getBillsFiltered(
        startDate: _filterStartDate,
        endDate: _filterEndDate,
        isPaid: _filterPaidStatus,
        customerId: _filterCustomerId,
        searchQuery: _searchController.text,
        offset: _currentPage * pageSize,
        limit: pageSize,
        sortDescending: _getSortDescending(),
      );

      // Get bills summary
      final summary = await repos.bill.getBillsSummary(
        startDate: _filterStartDate,
        endDate: _filterEndDate,
        isPaid: _filterPaidStatus,
        customerId: _filterCustomerId,
        searchQuery: _searchController.text,
      );

      // Manual calculation and verification of summary values
      double manualPaidAmount = 0.0;
      double manualUnpaidAmount = 0.0;

      for (var bill in bills) {
        if (bill.isPaid) {
          manualPaidAmount += bill.amount;
        } else if (bill.isPartiallyPaid && bill.partialAmount != null) {
          manualPaidAmount += bill.partialAmount!;
          manualUnpaidAmount += (bill.amount - bill.partialAmount!);
        } else {
          manualUnpaidAmount += bill.amount;
        }
      }

      // Ensure summary values are correct
      if (summary['paidAmount'] != manualPaidAmount ||
          summary['unpaidAmount'] != manualUnpaidAmount) {
        // Fix the summary values
        summary['paidAmount'] = manualPaidAmount;
        summary['unpaidAmount'] = manualUnpaidAmount;
      }

      // Load customer details for these bills
      final customerIds = bills.map((bill) => bill.customerId).toSet().toList();
      final customers =
          await repos.customer.getCustomersByIds(customerIds);
      final customersMap = {for (var c in customers) c.id: c};

      // Check if we have more data to load
      _hasMoreData = bills.length >= pageSize;

      // Always sort bills according to current sort option
      _sortBills(bills);

      // Group bills by date only for date-based sorting and when not searching/filtering
      Map<String, List<Bill>> groupedBills = {};
      bool shouldGroupByDate = !_isSearching &&
                              !_isFilteringActive &&
                              (_currentSortOption == SortOption.dateNewest || _currentSortOption == SortOption.dateOldest);

      if (shouldGroupByDate) {
        for (var bill in bills) {
          final dateKey = DateFormat('yyyy-MM-dd').format(bill.billDate);
          if (!groupedBills.containsKey(dateKey)) {
            groupedBills[dateKey] = [];
          }
          groupedBills[dateKey]!.add(bill);
        }

        // Sort date keys according to selected option
        final dateKeys = groupedBills.keys.toList();
        dateKeys.sort((a, b) => _getSortDescending() ? b.compareTo(a) : a.compareTo(b));

        setState(() {
          _bills = bills;
          _filteredBills = bills;
          _customersMap = customersMap;
          _groupedBills = groupedBills;
          _dateKeys = dateKeys;
          _billsSummary = summary;
          _isLoading = false;
          _isRefreshing = false;
        });
      } else {
        setState(() {
          if (_currentPage == 0) {
            _bills = bills;
            _filteredBills = bills;
          } else {
            _bills.addAll(bills);
            _filteredBills = _bills;
          }
          _customersMap = {..._customersMap, ...customersMap};
          _billsSummary = summary;
          _isLoading = false;
          _isRefreshing = false;
        });
      }
    } catch (e) {
      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _isRefreshing = false;
      });

      // Show error message - safe to use context as we've checked mounted
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading data: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _loadMoreBills() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _currentPage++;

      final repos = RepositoryService.instance;
      final bills = await repos.bill.getBillsFiltered(
        startDate: _filterStartDate,
        endDate: _filterEndDate,
        isPaid: _filterPaidStatus,
        customerId: _filterCustomerId,
        searchQuery: _searchController.text,
        offset: _currentPage * pageSize,
        limit: pageSize,
        sortDescending: _getSortDescending(),
      );

      // Get customer details for these bills
      final customerIds = bills.map((bill) => bill.customerId).toSet().toList();
      final customers =
          await repos.customer.getCustomersByIds(customerIds);
      final customersMap = {for (var c in customers) c.id: c};

      // Sort bills according to current sort option
      _sortBills(bills);

      setState(() {
        _hasMoreData = bills.length >= pageSize;
        _bills.addAll(bills);
        _filteredBills = _bills;
        _customersMap = {..._customersMap, ...customersMap};
        _isLoadingMore = false;
      });
    } catch (e) {
      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _isLoadingMore = false;
      });

      // Show error message - safe to use context as we've checked mounted
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading more data: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _refreshBills() async {
    setState(() {
      _currentPage = 0;
    });
    await _loadBills();
  }

  void _sortBills(List<Bill> bills) {
    switch (_currentSortOption) {
      case SortOption.dateNewest:
        bills.sort((a, b) => b.billDate.compareTo(a.billDate));
        break;
      case SortOption.dateOldest:
        bills.sort((a, b) => a.billDate.compareTo(b.billDate));
        break;
      case SortOption.amountHighest:
        bills.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case SortOption.amountLowest:
        bills.sort((a, b) => a.amount.compareTo(b.amount));
        break;
      case SortOption.customerNameAZ:
        bills.sort((a, b) {
          final nameA = _customersMap[a.customerId]?.name ?? '';
          final nameB = _customersMap[b.customerId]?.name ?? '';
          return nameA.compareTo(nameB);
        });
        break;
      case SortOption.customerNameZA:
        bills.sort((a, b) {
          final nameA = _customersMap[a.customerId]?.name ?? '';
          final nameB = _customersMap[b.customerId]?.name ?? '';
          return nameB.compareTo(nameA);
        });
        break;
      case SortOption.statusUnpaidFirst:
        bills.sort((a, b) {
          if (a.isPaid == b.isPaid) return 0;
          return a.isPaid ? 1 : -1;
        });
        break;
      case SortOption.statusPaidFirst:
        bills.sort((a, b) {
          if (a.isPaid == b.isPaid) return 0;
          return a.isPaid ? -1 : 1;
        });
        break;
    }
  }

  bool _getSortDescending() {
    switch (_currentSortOption) {
      case SortOption.dateNewest:
      case SortOption.amountHighest:
      case SortOption.customerNameZA:
      case SortOption.statusPaidFirst:
        return true;
      case SortOption.dateOldest:
      case SortOption.amountLowest:
      case SortOption.customerNameAZ:
      case SortOption.statusUnpaidFirst:
        return false;
    }
  }

  void _applyFilters({
    DateTime? startDate,
    DateTime? endDate,
    bool? paidStatus,
    int? customerId,
  }) {
    setState(() {
      _filterStartDate = startDate;
      _filterEndDate = endDate;
      _filterPaidStatus = paidStatus;
      _filterCustomerId = customerId;
      _isFilteringActive = startDate != null ||
          endDate != null ||
          paidStatus != null ||
          customerId != null;
    });

    // Reload data with new filters
    _loadBills(resetPagination: true);
  }

  void _clearFilters() {
    setState(() {
      _filterStartDate = null;
      _filterEndDate = null;
      _filterPaidStatus = null;
      _filterCustomerId = null;
      _isFilteringActive = false;
    });

    // Reload data without filters
    _loadBills(resetPagination: true);
  }

  // Generate PDF for transactions
  Future<void> _generateTransactionsPdf() async {
    if (!mounted) return;

    try {
      // Get all transactions that match the current filters
      // We need to get all transactions, not just the paginated ones
      final repos = RepositoryService.instance;
      final allFilteredBills = await repos.bill.getBillsFiltered(
        startDate: _filterStartDate,
        endDate: _filterEndDate,
        isPaid: _filterPaidStatus,
        customerId: _filterCustomerId,
        searchQuery: _searchController.text,
        // Don't use pagination for PDF generation
        offset: 0,
        limit: 1000, // Use a large limit to get all bills
        sortDescending: _getSortDescending(),
      );

      if (!mounted) return;

      // Load customer details for these bills
      final customerIds =
          allFilteredBills.map((bill) => bill.customerId).toSet().toList();
      final customers =
          await repos.customer.getCustomersByIds(customerIds);
      final customersMap = {for (var c in customers) c.id: c};

      // Create PDF data
      final Map<String, dynamic> pdfData = {
        'bills': allFilteredBills,
        'customersMap': customersMap,
        'summary': _billsSummary,
        'filters': {
          'startDate': _filterStartDate,
          'endDate': _filterEndDate,
          'isPaid': _filterPaidStatus,
          'customerId': _filterCustomerId,
          'searchQuery':
              _searchController.text.isNotEmpty ? _searchController.text : null,
          'sortOption': _currentSortOption.label,
        },
      };

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Use the universal PDF service to generate the PDF with modern design
      await UniversalPdfService.handlePdf(
        context,
        pdfData,
        autoOpen: true,
        showSaveOption: true,
        showShareOption: true,
        pdfSettings: PdfSettings.modern().copyWith(
          additionalSettings: {
            'footerText': 'Transactions Report',
            'title': 'Transactions Report',
          },
        ),
      );
    } catch (e) {
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error generating PDF: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('Filter Transactions'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date range filter
                  const Text(
                    'Date Range',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final picked = await showDatePicker(
                              context: context,
                              initialDate: _filterStartDate ?? DateTime.now(),
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now(),
                            );
                            if (picked != null) {
                              setState(() {
                                _filterStartDate = picked;
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              _filterStartDate != null
                                  ? DateFormat('MMM d, yyyy')
                                      .format(_filterStartDate!)
                                  : 'Start Date',
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final picked = await showDatePicker(
                              context: context,
                              initialDate: _filterEndDate ?? DateTime.now(),
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now(),
                            );
                            if (picked != null) {
                              setState(() {
                                _filterEndDate = picked;
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              _filterEndDate != null
                                  ? DateFormat('MMM d, yyyy')
                                      .format(_filterEndDate!)
                                  : 'End Date',
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Payment status filter
                  const Text(
                    'Payment Status',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      FilterChip(
                        label: const Text('All'),
                        selected: _filterPaidStatus == null,
                        onSelected: (selected) {
                          setState(() {
                            _filterPaidStatus = null;
                          });
                        },
                        backgroundColor: Colors.blue,
                        selectedColor: Colors.indigo,
                        labelStyle: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        showCheckmark: false,
                      ),
                      FilterChip(
                        label: const Text('Paid'),
                        selected: _filterPaidStatus == true,
                        onSelected: (selected) {
                          setState(() {
                            _filterPaidStatus = selected ? true : null;
                          });
                        },
                        backgroundColor:
                            const Color(0xFF2E7D32), // App theme green
                        selectedColor: const Color(0xFF1B5E20), // Darker green
                        labelStyle: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        showCheckmark: false,
                      ),
                      FilterChip(
                        label: const Text('Unpaid'),
                        selected: _filterPaidStatus == false,
                        onSelected: (selected) {
                          setState(() {
                            _filterPaidStatus = selected ? false : null;
                          });
                        },
                        backgroundColor: Colors.red,
                        selectedColor: Colors.red.shade900,
                        labelStyle: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        showCheckmark: false,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Customer filter
                  const Text(
                    'Customer',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<int?>(
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    value: _filterCustomerId,
                    hint: const Text('Select Customer'),
                    items: [
                      const DropdownMenuItem<int?>(
                        value: null,
                        child: Text('All Customers'),
                      ),
                      ..._customersMap.entries.map((entry) {
                        return DropdownMenuItem<int?>(
                          value: entry.key,
                          child: Text(entry.value.name),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _filterCustomerId = value;
                      });
                    },
                  ),
                ],
              ),
            ),
            actions: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Wrap(
                  alignment: WrapAlignment.spaceEvenly,
                  spacing: 8.0,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                      ),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _clearFilters();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                      ),
                      child: const Text('Clear'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _applyFilters(
                          startDate: _filterStartDate,
                          endDate: _filterEndDate,
                          paidStatus: _filterPaidStatus,
                          customerId: _filterCustomerId,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2E7D32),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                      ),
                      child: const Text('Apply'),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope<bool>(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        // Show exit confirmation dialog when user tries to exit the app
        final shouldExit = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Exit App'),
            content: const Text('Are you sure you want to exit the app?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('No'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
                child: const Text('Yes'),
              ),
            ],
          ),
        );

        if (shouldExit == true) {
          // Actually exit the app
          NavigationHelper.exitApp();
        }
      },
      child: Scaffold(
        appBar: TubewellAppBar(
          title: 'Daily Register',
          showBackButton: false,
          currentScreen: 'transactions',
          onPdfPressed: _generateTransactionsPdf,
        ),
        drawer: const AppDrawer(currentScreen: 'transactions'),
        body: Column(
          children: [
            // Search and filter bar - keep fixed at top
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search by name, ID, or amount',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                },
                              )
                            : null,
                        contentPadding:
                            const EdgeInsets.symmetric(vertical: 10),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide(
                              color: Colors.indigo.shade400, width: 2),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: _isFilteringActive
                          ? const Color(0xFF2E7D32)
                          : Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.filter_list,
                        color: _isFilteringActive
                            ? Colors.white
                            : Colors.grey.shade700,
                      ),
                      onPressed: _showFilterDialog,
                      tooltip: 'Filter',
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: PopupMenuButton<SortOption>(
                      tooltip: 'Sort by ${_currentSortOption.label}',
                      onSelected: (option) {
                        setState(() {
                          _currentSortOption = option;
                        });
                        _loadBills(resetPagination: true);
                      },
                      itemBuilder: (context) => SortOption.values.map((option) {
                        final isSelected = option == _currentSortOption;
                        return PopupMenuItem<SortOption>(
                          value: option,
                          child: Row(
                            children: [
                              Icon(
                                option.icon,
                                color: isSelected
                                    ? const Color(0xFF2E7D32)
                                    : Colors.grey.shade700,
                                size: 18,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  option.label,
                                  style: TextStyle(
                                    color: isSelected
                                        ? const Color(0xFF2E7D32)
                                        : Colors.black,
                                    fontWeight: isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                  ),
                                ),
                              ),
                              if (isSelected)
                                const Icon(Icons.check,
                                    color: Color(0xFF2E7D32), size: 18),
                            ],
                          ),
                        );
                      }).toList(),
                      icon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.sort,
                            color: Colors.grey.shade700,
                            size: 20,
                          ),
                          const SizedBox(width: 4),
                          Container(
                            constraints: const BoxConstraints(maxWidth: 24),
                            child: Stack(
                              alignment: Alignment.bottomRight,
                              children: [
                                const SizedBox(width: 24, height: 24),
                                Positioned(
                                  right: 0,
                                  bottom: 0,
                                  child: Container(
                                    width: 8,
                                    height: 8,
                                    decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Color(0xFF2E7D32),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Filter and search indicators
            if (_isSearching || _isFilteringActive)
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
                child: Row(
                  children: [
                    Text(
                      'Found ${_billsSummary['totalCount'] ?? 0} results',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                    const Spacer(),
                    if (_isFilteringActive)
                      Chip(
                        label: const Text('Filters Applied'),
                        deleteIcon: const Icon(Icons.close, size: 18),
                        onDeleted: _clearFilters,
                        backgroundColor: const Color(0xFFE8F5E9),
                        deleteIconColor: const Color(0xFF2E7D32),
                        labelStyle: const TextStyle(color: Color(0xFF2E7D32)),
                      ),
                    if (_isSearching)
                      TextButton(
                        onPressed: () {
                          _searchController.clear();
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: const Color(0xFF2E7D32),
                        ),
                        child: const Text('Clear Search'),
                      ),
                  ],
                ),
              ),

            // Scrollable content area
            Expanded(
              child: RefreshIndicator(
                onRefresh: _refreshBills,
                color: const Color(0xFF2E7D32),
                child: _isLoading && _currentPage == 0
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredBills.isEmpty
                        ? EmptyStateWidget(
                            icon: _isSearching
                                ? Icons.search_off
                                : Icons.receipt_long,
                            title: _isSearching
                                ? 'No Results Found'
                                : 'No Transactions Yet',
                            message: _isSearching
                                ? 'Try a different search term'
                                : 'Record your first transaction to get started.',
                            buttonText: _isSearching
                                ? 'Clear Search'
                                : 'Add Transaction',
                            onButtonPressed: () {
                              if (_isSearching) {
                                _searchController.clear();
                              } else {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const TransactionFormScreen(),
                                  ),
                                ).then((result) {
                                  if (result == true) {
                                    _refreshBills();
                                  }
                                });
                              }
                            },
                          )
                        : _buildScrollableContent(),
              ),
            ),
          ],
        ),
        floatingActionButton: UniversalFab(
          type: FabType.transaction,
          heroTag: 'fab-transactions',
          onResult: (result) {
            if (result) {
              _refreshBills();
            }
          },
        ),
      ),
    );
  }

  Widget _buildScrollableContent() {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        // Summary card as a sliver
        if (!_isLoading)
          SliverToBoxAdapter(
            child: _buildCustomSummaryCard(),
          ),

        // Transaction list as a sliver
        _buildTransactionsList(),
      ],
    );
  }

  Widget _buildTransactionsList() {
    // Use the same condition as data loading: only group by date for date-based sorting when not searching/filtering
    bool shouldGroupByDate = !_isSearching &&
                            !_isFilteringActive &&
                            (_currentSortOption == SortOption.dateNewest || _currentSortOption == SortOption.dateOldest);

    if (!shouldGroupByDate) {
      // Show a flat list without date grouping for non-date sorts, searching, or filtering
      return SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index == _filteredBills.length) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              );
            }

            final bill = _filteredBills[index];
            final customer = _customersMap[bill.customerId];
            return _buildBillCard(bill, customer);
          },
          childCount: _filteredBills.length + (_isLoadingMore ? 1 : 0),
        ),
      );
    } else {
      // Group by date only for date-based sorting
      return SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, dateIndex) {
            final dateKey = _dateKeys[dateIndex];
            final dateFormatted =
                DateFormat('EEEE, dd MMMM yyyy').format(DateTime.parse(dateKey));
            final billsForDate = _groupedBills[dateKey] ?? [];

            // Bills are already sorted within each date group during data loading

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Date header
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: Container(
                    width: double.infinity,
                    padding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.teal.shade700,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today,
                            color: Colors.white, size: 18),
                        const SizedBox(width: 8),
                        Text(
                          dateFormatted,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          '${billsForDate.length} ${billsForDate.length == 1 ? 'transaction' : 'transactions'}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Bills for this date
                ...billsForDate.map((bill) {
                  final customer = _customersMap[bill.customerId];
                  return _buildBillCard(bill, customer);
                }),
              ],
            );
          },
          childCount: _dateKeys.length,
        ),
      );
    }
  }

  Widget _buildBillCard(Bill bill, Customer? customer) {
    final customerName = customer?.name ?? 'Unknown Customer';
    final billId = '#${bill.id.toString().padLeft(3, '0')}';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: bill.isPaid
                ? Colors.green.shade400
                : bill.isPartiallyPaid
                    ? Colors.blue.shade400
                    : Colors.red.shade400,
            width: 2.0,
          ),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => BillDetailsScreen(
                  bill: bill,
                  customer: customer,
                ),
              ),
            ).then((_) => _refreshBills());
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row 1: Bill ID and Customer name
                Row(
                  children: [
                    // Bill ID with icon
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.indigo.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.indigo.shade200),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.receipt,
                              size: 14, color: Colors.indigo.shade700),
                          const SizedBox(width: 4),
                          Text(
                            "Bill $billId",
                            style: TextStyle(
                              color: Colors.indigo.shade800,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Customer name with person icon
                    Expanded(
                      child: GestureDetector(
                        onTap: customer != null
                            ? () => Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => CustomerDetailScreen(
                                      customer: customer,
                                    ),
                                  ),
                                )
                            : null,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.teal.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.teal.shade200),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.person,
                                color: Colors.teal.shade700,
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  customerName,
                                  style: TextStyle(
                                    color: Colors.teal.shade800,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Edit button
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => TransactionFormScreen(
                              existingBill: bill,
                              selectedCustomer: _customersMap[bill.customerId],
                            ),
                          ),
                        ).then((result) {
                          if (result == true) {
                            _refreshBills();
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          color: Colors.cyan.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.cyan.shade200),
                        ),
                        child: Icon(Icons.edit,
                            color: Colors.cyan.shade700, size: 14),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Row 2: Duration and Amount
                Row(
                  children: [
                    // Time duration with icon
                    Expanded(
                      child: DurationChip(
                        hours: bill.durationHoursWhole,
                        minutes: bill.durationMinutes,
                        showLabel: false,
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Amount with icon
                    Expanded(
                      child: AmountChip(
                        amount: bill.amount,
                        showLabel: false,
                        customColor: bill.isPaid
                            ? Colors.green.shade700
                            : bill.isPartiallyPaid
                                ? Colors.blue.shade700
                                : Colors.red.shade700,
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Delete button
                    GestureDetector(
                      onTap: () => _showDeleteConfirmation(bill),
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Icon(Icons.delete,
                            color: Colors.red.shade700, size: 14),
                      ),
                    ),
                  ],
                ),

                // Add partial payment indicator after the duration and amount row
                if (bill.isPartiallyPaid && bill.partialAmount != null) ...[
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.payment,
                            size: 14, color: Colors.blue.shade700),
                        const SizedBox(width: 4),
                        Text(
                          'Paid: ${CurrencyService.formatCurrency(bill.partialAmount!)} of ${CurrencyService.formatCurrency(bill.amount)}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                // Show date on flat view (when not grouping by date)
                if (_isSearching || _isFilteringActive ||
                    (_currentSortOption != SortOption.dateNewest && _currentSortOption != SortOption.dateOldest)) ...[
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Icon(Icons.event, size: 14, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        DateFormat('MMM dd, yyyy').format(bill.billDate),
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmation(Bill bill) {
    final Customer? customer = _customersMap[bill.customerId];
    final String customerName = customer?.name ?? 'Unknown Customer';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Water'),
        content: Text(
            'Are you sure you want to delete this Water for $customerName?\n\n'
            'Amount: ${CurrencyService.formatCurrency(bill.amount)}\n'
            'This will also remove any associated payments.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteBill(bill);
            },
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteBill(Bill bill) async {
    try {
      final result = await BillingService.deleteBill(bill);

      // Check if widget is still mounted after async operation
      if (!mounted) return;

      if (result) {
        _refreshBills();
        // Safe to use context as we've checked mounted
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Water deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Safe to use context as we've checked mounted
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to delete Water'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // Check if widget is still mounted after async operation
      if (!mounted) return;

      // Safe to use context as we've checked mounted
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error deleting Water: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildCustomSummaryCard() {
    // Ensure we have valid summary values even if there are no transactions
    final int totalCount = _billsSummary['totalCount']?.toInt() ?? 0;
    final double totalAmount = _billsSummary['totalAmount']?.toDouble() ?? 0.0;
    final double paidAmount = _billsSummary['paidAmount']?.toDouble() ?? 0.0;
    final double unpaidAmount =
        _billsSummary['unpaidAmount']?.toDouble() ?? 0.0;

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  'Transactions Summary',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade900,
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                '$totalCount transactions',
                style: TextStyle(
                  color: Colors.blue.shade800,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Main amount with highlight
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade300),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      color: Colors.blue.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Total Amount',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    CurrencyService.formatCurrency(totalAmount),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Detailed stats
          Row(
            children: [
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Paid',
                  value: CurrencyService.formatCurrency(paidAmount),
                  icon: Icons.check_circle,
                  iconColor: Colors.blue.shade700,
                  bgColor: Colors.blue.shade50,
                  borderColor: Colors.blue.shade200,
                  textColor: Colors.blue.shade800,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Unpaid',
                  value: CurrencyService.formatCurrency(unpaidAmount),
                  icon: Icons.money_off,
                  iconColor: Colors.red.shade700,
                  bgColor: Colors.red.shade50,
                  borderColor: Colors.red.shade200,
                  textColor: Colors.red.shade800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
    required Color bgColor,
    required Color textColor,
    required Color borderColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: iconColor, size: 16),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    color: textColor,
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: textColor,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
