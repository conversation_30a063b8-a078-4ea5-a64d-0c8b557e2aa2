import 'package:sqflite/sqflite.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/models/payment_allocation.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/account_service.dart';

/// Repository for payment and payment allocation operations
class PaymentRepository {
  final DatabaseService _dbService = DatabaseService.instance;

  /// Get current account ID from AccountService
  String? get _currentAccountId => AccountService.currentAccount?.id;

  /// Get a payment by ID
  Future<Payment?> getPaymentById(int id) async {
    final db = await _dbService.database;
    final maps = await db.query(
      'payments',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Payment.fromMap(maps.first);
    }
    return null;
  }

  /// Save a payment (insert or update)
  Future<int> savePayment(Payment payment) async {
    final db = await _dbService.database;

    // Add the current account ID to the payment data
    final paymentData = payment.toMap();
    paymentData['accountId'] = _currentAccountId;

    if (payment.id == null) {
      // Insert new payment
      return await db.insert('payments', paymentData);
    } else {
      // Update existing payment
      await db.update(
        'payments',
        paymentData,
        where: 'id = ?',
        whereArgs: [payment.id],
      );
      return payment.id!;
    }
  }

  /// Get payments by customer ID with pagination and search
  Future<List<Payment>> getPaymentsByCustomer(
    int customerId, {
    int offset = 0,
    int limit = 20,
    String? searchQuery,
    String? paymentMethod,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await _dbService.database;
    
    String whereClause = 'customerId = ?';
    List<dynamic> whereArgs = [customerId];

    // Account filtering
    if (_currentAccountId != null) {
      whereClause += ' AND (accountId = ? OR accountId IS NULL)';
      whereArgs.add(_currentAccountId);
    } else {
      whereClause += ' AND accountId IS NULL';
    }

    // Search query filtering
    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause += ' AND (remarks LIKE ? OR CAST(amount AS TEXT) LIKE ?)';
      whereArgs.addAll(['%$searchQuery%', '%$searchQuery%']);
    }

    // Payment method filtering
    if (paymentMethod != null && paymentMethod.isNotEmpty) {
      whereClause += ' AND paymentMethod = ?';
      whereArgs.add(paymentMethod);
    }

    // Date range filtering
    if (startDate != null) {
      whereClause += ' AND paymentDate >= ?';
      whereArgs.add(startDate.toIso8601String());
    }
    if (endDate != null) {
      whereClause += ' AND paymentDate <= ?';
      whereArgs.add(endDate.toIso8601String());
    }

    final maps = await db.query(
      'payments',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'paymentDate DESC',
      limit: limit,
      offset: offset,
    );

    return maps.map((map) => Payment.fromMap(map)).toList();
  }

  /// Get payments by customer with pagination
  Future<List<Payment>> getPaymentsByCustomerPaginated(
    int customerId, {
    int page = 0,
    int pageSize = 20,
  }) async {
    final db = await _dbService.database;
    final offset = page * pageSize;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'payments',
        where: 'customerId = ? AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [customerId, _currentAccountId],
        orderBy: 'paymentDate DESC',
        limit: pageSize,
        offset: offset,
      );
    } else {
      maps = await db.query(
        'payments',
        where: 'customerId = ? AND accountId IS NULL',
        whereArgs: [customerId],
        orderBy: 'paymentDate DESC',
        limit: pageSize,
        offset: offset,
      );
    }

    return maps.map((map) => Payment.fromMap(map)).toList();
  }

  /// Get payments by bill ID using optimized single query with UNION
  Future<List<Payment>> getPaymentsByBill(int billId) async {
    final db = await _dbService.database;

    // Use a single query with UNION to get both direct payments and allocation payments
    String query = '''
      SELECT DISTINCT p.* FROM payments p
      WHERE p.billId = ?
      UNION
      SELECT DISTINCT p.* FROM payments p
      INNER JOIN payment_allocations pa ON p.id = pa.paymentId
      WHERE pa.billId = ?
      ORDER BY paymentDate DESC
    ''';

    List<dynamic> args = [billId, billId];

    // Add account filtering
    if (_currentAccountId != null) {
      query = '''
        SELECT DISTINCT p.* FROM payments p
        WHERE p.billId = ? AND (p.accountId = ? OR p.accountId IS NULL)
        UNION
        SELECT DISTINCT p.* FROM payments p
        INNER JOIN payment_allocations pa ON p.id = pa.paymentId
        WHERE pa.billId = ? AND (p.accountId = ? OR p.accountId IS NULL)
        ORDER BY paymentDate DESC
      ''';
      args = [billId, _currentAccountId, billId, _currentAccountId];
    } else {
      query = '''
        SELECT DISTINCT p.* FROM payments p
        WHERE p.billId = ? AND p.accountId IS NULL
        UNION
        SELECT DISTINCT p.* FROM payments p
        INNER JOIN payment_allocations pa ON p.id = pa.paymentId
        WHERE pa.billId = ? AND p.accountId IS NULL
        ORDER BY paymentDate DESC
      ''';
      args = [billId, billId];
    }

    final maps = await db.rawQuery(query, args);
    return maps.map((map) => Payment.fromMap(map)).toList();
  }

  /// Get all payments for the current account
  Future<List<Payment>> getAllPayments() async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'payments',
        where: 'accountId = ? OR accountId IS NULL',
        whereArgs: [_currentAccountId],
        orderBy: 'paymentDate DESC',
      );
    } else {
      maps = await db.query(
        'payments',
        where: 'accountId IS NULL',
        orderBy: 'paymentDate DESC',
      );
    }

    return maps.map((map) => Payment.fromMap(map)).toList();
  }

  /// Delete a payment
  Future<void> deletePayment(int id) async {
    final db = await _dbService.database;
    await db.delete(
      'payments',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // --- Payment Allocation Methods ---

  /// Save a payment allocation
  Future<int> savePaymentAllocation(PaymentAllocation allocation) async {
    final db = await _dbService.database;

    // Add the current account ID to the allocation data
    final allocationData = allocation.toMap();
    allocationData['accountId'] = _currentAccountId;

    if (allocation.id == null) {
      // Insert new allocation
      return await db.insert('payment_allocations', allocationData);
    } else {
      // Update existing allocation
      await db.update(
        'payment_allocations',
        allocationData,
        where: 'id = ?',
        whereArgs: [allocation.id],
      );
      return allocation.id!;
    }
  }

  /// Get payment allocations by bill ID
  Future<List<PaymentAllocation>> getPaymentAllocationsByBillId(int billId) async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'payment_allocations',
        where: 'billId = ? AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [billId, _currentAccountId],
        orderBy: 'createdAt DESC',
      );
    } else {
      maps = await db.query(
        'payment_allocations',
        where: 'billId = ? AND accountId IS NULL',
        whereArgs: [billId],
        orderBy: 'createdAt DESC',
      );
    }

    return maps.map((map) => PaymentAllocation.fromMap(map)).toList();
  }

  /// Get payment allocations by payment ID
  Future<List<PaymentAllocation>> getPaymentAllocationsByPaymentId(int paymentId) async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'payment_allocations',
        where: 'paymentId = ? AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [paymentId, _currentAccountId],
        orderBy: 'createdAt DESC',
      );
    } else {
      maps = await db.query(
        'payment_allocations',
        where: 'paymentId = ? AND accountId IS NULL',
        whereArgs: [paymentId],
        orderBy: 'createdAt DESC',
      );
    }

    return maps.map((map) => PaymentAllocation.fromMap(map)).toList();
  }

  /// Delete a payment allocation
  Future<void> deletePaymentAllocation(int id) async {
    final db = await _dbService.database;
    await db.delete(
      'payment_allocations',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get payment summary for a customer
  Future<Map<String, num>> getPaymentSummaryByCustomer(int customerId) async {
    final db = await _dbService.database;
    
    String query = '''
      SELECT
        COUNT(*) as totalCount,
        SUM(amount) as totalAmount
      FROM payments
      WHERE customerId = ?
    ''';
    
    List<dynamic> args = [customerId];

    // Account filtering
    if (_currentAccountId != null) {
      query += ' AND (accountId = ? OR accountId IS NULL)';
      args.add(_currentAccountId);
    } else {
      query += ' AND accountId IS NULL';
    }

    final results = await db.rawQuery(query, args);
    
    if (results.isNotEmpty) {
      final result = results.first;
      return {
        'totalCount': result['totalCount'] ?? 0,
        'totalAmount': result['totalAmount'] ?? 0.0,
      };
    }
    
    return {
      'totalCount': 0,
      'totalAmount': 0.0,
    };
  }
}
