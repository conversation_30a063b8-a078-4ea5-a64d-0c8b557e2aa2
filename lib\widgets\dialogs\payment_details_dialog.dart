import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing/models/payment.dart';

/// A dialog widget for displaying detailed payment information
class PaymentDetailsDialog extends StatelessWidget {
  final Payment payment;

  const PaymentDetailsDialog({
    super.key,
    required this.payment,
  });

  /// Static method to show the payment details dialog
  static void show(BuildContext context, Payment payment) {
    showDialog(
      context: context,
      builder: (context) => PaymentDetailsDialog(payment: payment),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.payment, color: Colors.blue.shade700),
          const SizedBox(width: 8),
          Text('Payment #${payment.id}'),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildInfoRow('Amount:', 'Rs. ${payment.amount.toStringAsFixed(2)}'),
            _buildInfoRow(
              'Date:', 
              DateFormat('dd MMM yyyy').format(payment.paymentDate)
            ),
            _buildInfoRow(
              'Method:', 
              _getPaymentMethodLabel(payment.paymentMethod)
            ),
            _buildInfoRow(
              'Linked to Bill:',
              payment.billId > 0 ? '#${payment.billId}' : 'None (Credit)'
            ),
            if (payment.remarks != null && payment.remarks!.isNotEmpty)
              _buildInfoRow('Remarks:', payment.remarks!),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('CLOSE'),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getPaymentMethodLabel(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return 'Cash';
      case 'bank_transfer':
      case 'bank transfer':
        return 'Bank Transfer';
      case 'check':
        return 'Check';
      case 'upi':
        return 'UPI';
      case 'other':
        return 'Other';
      default:
        return method;
    }
  }
}
