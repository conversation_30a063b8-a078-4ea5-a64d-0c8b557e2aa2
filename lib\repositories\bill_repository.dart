
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/account_service.dart';

/// Repository for bill-related database operations
class BillRepository {
  final DatabaseService _dbService = DatabaseService.instance;

  /// Get current account ID from AccountService
  String? get _currentAccountId => AccountService.currentAccount?.id;

  /// Get a bill by ID
  Future<Bill?> getBillById(int id) async {
    final db = await _dbService.database;
    final maps = await db.query(
      'bills',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Bill.fromMap(maps.first);
    }
    return null;
  }

  /// Get all bills for the current account
  Future<List<Bill>> getAllBills() async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;
    
    if (_currentAccountId != null) {
      maps = await db.query(
        'bills',
        where: 'accountId = ? OR accountId IS NULL',
        whereArgs: [_currentAccountId],
        orderBy: 'billDate DESC',
      );
    } else {
      maps = await db.query(
        'bills',
        where: 'accountId IS NULL',
        orderBy: 'billDate DESC',
      );
    }

    return maps.map((map) => Bill.fromMap(map)).toList();
  }

  /// Save a bill (insert or update)
  Future<int> saveBill(Bill bill) async {
    final db = await _dbService.database;

    // Add the current account ID to the bill data
    final billData = bill.toMap();
    billData['accountId'] = _currentAccountId;

    if (bill.id == 0) {
      // Insert new bill
      return await db.insert('bills', billData);
    } else {
      // Update existing bill
      await db.update(
        'bills',
        billData,
        where: 'id = ?',
        whereArgs: [bill.id],
      );
      return bill.id;
    }
  }

  /// Get bills by customer ID
  Future<List<Bill>> getBillsByCustomer(int customerId) async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'bills',
        where: 'customerId = ? AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [customerId, _currentAccountId],
        orderBy: 'billDate DESC',
      );
    } else {
      maps = await db.query(
        'bills',
        where: 'customerId = ? AND accountId IS NULL',
        whereArgs: [customerId],
        orderBy: 'billDate DESC',
      );
    }

    return maps.map((map) => Bill.fromMap(map)).toList();
  }

  /// Get bills by customer with pagination
  Future<List<Bill>> getBillsByCustomerPaginated(
    int customerId, {
    int page = 0,
    int pageSize = 20,
  }) async {
    final db = await _dbService.database;
    final offset = page * pageSize;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'bills',
        where: 'customerId = ? AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [customerId, _currentAccountId],
        orderBy: 'billDate DESC',
        limit: pageSize,
        offset: offset,
      );
    } else {
      maps = await db.query(
        'bills',
        where: 'customerId = ? AND accountId IS NULL',
        whereArgs: [customerId],
        orderBy: 'billDate DESC',
        limit: pageSize,
        offset: offset,
      );
    }

    return maps.map((map) => Bill.fromMap(map)).toList();
  }

  /// Get filtered bills with improved performance using JOINs
  Future<List<Bill>> getBillsFiltered({
    DateTime? startDate,
    DateTime? endDate,
    bool? isPaid,
    int? customerId,
    String? searchQuery,
    int? limit,
    int? offset,
  }) async {
    final db = await _dbService.database;
    
    // Build the query with JOIN for efficient customer name searching
    String query = '''
      SELECT b.* FROM bills b
      LEFT JOIN customers c ON b.customerId = c.id
      WHERE 1=1
    ''';
    
    List<dynamic> args = [];

    // Account filtering
    if (_currentAccountId != null) {
      query += ' AND (b.accountId = ? OR b.accountId IS NULL)';
      args.add(_currentAccountId);
    } else {
      query += ' AND b.accountId IS NULL';
    }

    // Date filtering
    if (startDate != null) {
      query += ' AND b.billDate >= ?';
      args.add(startDate.toIso8601String());
    }
    if (endDate != null) {
      query += ' AND b.billDate <= ?';
      args.add(endDate.toIso8601String());
    }

    // Payment status filtering
    if (isPaid != null) {
      query += ' AND b.isPaid = ?';
      args.add(isPaid ? 1 : 0);
    }

    // Customer filtering
    if (customerId != null) {
      query += ' AND b.customerId = ?';
      args.add(customerId);
    }

    // Search query (customer name or bill ID)
    if (searchQuery != null && searchQuery.isNotEmpty) {
      query += ' AND (c.name LIKE ? OR CAST(b.id AS TEXT) LIKE ? OR CAST(b.amount AS TEXT) LIKE ?)';
      final searchPattern = '%$searchQuery%';
      args.addAll([searchPattern, searchPattern, searchPattern]);
    }

    // Order by date descending
    query += ' ORDER BY b.billDate DESC';

    // Pagination
    if (limit != null) {
      query += ' LIMIT ?';
      args.add(limit);
      if (offset != null) {
        query += ' OFFSET ?';
        args.add(offset);
      }
    }

    final maps = await db.rawQuery(query, args);
    return maps.map((map) => Bill.fromMap(map)).toList();
  }

  /// Get bills summary with efficient single query
  Future<Map<String, num>> getBillsSummary({
    DateTime? startDate,
    DateTime? endDate,
    bool? isPaid,
    int? customerId,
    String? searchQuery,
  }) async {
    final db = await _dbService.database;
    
    // Build the query with JOIN for efficient searching
    String query = '''
      SELECT
        COUNT(*) as totalCount,
        SUM(b.amount) as totalAmount,
        SUM(CASE WHEN b.isPaid = 1 THEN b.amount ELSE 0 END) as paidAmount,
        SUM(CASE WHEN b.isPaid = 0 THEN b.amount ELSE 0 END) as unpaidAmount,
        COUNT(CASE WHEN b.isPaid = 1 THEN 1 END) as paidCount,
        COUNT(CASE WHEN b.isPaid = 0 THEN 1 END) as unpaidCount
      FROM bills b
      LEFT JOIN customers c ON b.customerId = c.id
      WHERE 1=1
    ''';
    
    List<dynamic> args = [];

    // Account filtering
    if (_currentAccountId != null) {
      query += ' AND (b.accountId = ? OR b.accountId IS NULL)';
      args.add(_currentAccountId);
    } else {
      query += ' AND b.accountId IS NULL';
    }

    // Date filtering
    if (startDate != null) {
      query += ' AND b.billDate >= ?';
      args.add(startDate.toIso8601String());
    }
    if (endDate != null) {
      query += ' AND b.billDate <= ?';
      args.add(endDate.toIso8601String());
    }

    // Payment status filtering
    if (isPaid != null) {
      query += ' AND b.isPaid = ?';
      args.add(isPaid ? 1 : 0);
    }

    // Customer filtering
    if (customerId != null) {
      query += ' AND b.customerId = ?';
      args.add(customerId);
    }

    // Search query
    if (searchQuery != null && searchQuery.isNotEmpty) {
      query += ' AND (c.name LIKE ? OR CAST(b.id AS TEXT) LIKE ? OR CAST(b.amount AS TEXT) LIKE ?)';
      final searchPattern = '%$searchQuery%';
      args.addAll([searchPattern, searchPattern, searchPattern]);
    }

    final results = await db.rawQuery(query, args);
    
    if (results.isNotEmpty) {
      final result = results.first;
      return {
        'totalCount': (result['totalCount'] as num?) ?? 0,
        'totalAmount': (result['totalAmount'] as num?) ?? 0.0,
        'paidAmount': (result['paidAmount'] as num?) ?? 0.0,
        'unpaidAmount': (result['unpaidAmount'] as num?) ?? 0.0,
        'paidCount': (result['paidCount'] as num?) ?? 0,
        'unpaidCount': (result['unpaidCount'] as num?) ?? 0,
      };
    }
    
    return {
      'totalCount': 0,
      'totalAmount': 0.0,
      'paidAmount': 0.0,
      'unpaidAmount': 0.0,
      'paidCount': 0,
      'unpaidCount': 0,
    };
  }

  /// Get recent bills
  Future<List<Bill>> getRecentBills({int limit = 5}) async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'bills',
        where: 'accountId = ? OR accountId IS NULL',
        whereArgs: [_currentAccountId],
        orderBy: 'billDate DESC',
        limit: limit,
      );
    } else {
      maps = await db.query(
        'bills',
        where: 'accountId IS NULL',
        orderBy: 'billDate DESC',
        limit: limit,
      );
    }

    return maps.map((map) => Bill.fromMap(map)).toList();
  }

  /// Update bill payment status
  Future<void> updateBillPaymentStatus(int billId, bool isPaid, {double? paidAmount, String? paidDate}) async {
    final db = await _dbService.database;
    final updateData = <String, dynamic>{
      'isPaid': isPaid ? 1 : 0,
    };
    
    if (paidAmount != null) {
      updateData['paidAmount'] = paidAmount;
    }
    if (paidDate != null) {
      updateData['paidDate'] = paidDate;
    }

    await db.update(
      'bills',
      updateData,
      where: 'id = ?',
      whereArgs: [billId],
    );
  }

  /// Delete a bill
  Future<void> deleteBill(int id) async {
    final db = await _dbService.database;
    await db.delete(
      'bills',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get complete bill details with customer and payments in a single optimized operation
  /// This method consolidates multiple database calls into one efficient query
  Future<Map<String, dynamic>?> getBillDetailsOptimized(int billId) async {
    final db = await _dbService.database;

    try {
      // Get the bill first
      final billResult = await db.query(
        'bills',
        where: 'id = ?',
        whereArgs: [billId],
      );

      if (billResult.isEmpty) {
        return null;
      }

      final billMap = billResult.first;
      final bill = Bill.fromMap(billMap);

      // Get the customer
      final customerResult = await db.query(
        'customers',
        where: 'id = ?',
        whereArgs: [bill.customerId],
      );

      Customer? customer;
      if (customerResult.isNotEmpty) {
        customer = Customer.fromMap(customerResult.first);
      }

      // Get payments directly linked to this bill using optimized query
      final directPayments = await db.rawQuery('''
        SELECT DISTINCT p.*
        FROM payments p
        WHERE p.billId = ?

        UNION

        SELECT DISTINCT p.*
        FROM payments p
        INNER JOIN payment_allocations pa ON p.id = pa.paymentId
        WHERE pa.billId = ?

        ORDER BY paymentDate ASC
      ''', [billId, billId]);

      final payments = directPayments.map((p) => Payment.fromMap(p)).toList();

      // Get the bill with updated payment status (read-only calculation)
      final billWithStatus = await getBillById(billId);

      return {
        'bill': billWithStatus ?? bill,
        'customer': customer,
        'payments': payments,
      };
    } catch (e) {
      // Log error and return null
      return null;
    }
  }
}
